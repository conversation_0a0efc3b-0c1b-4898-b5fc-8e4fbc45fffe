// API Helper Functions
class API {
  static async request(url, options = {}) {
    try {
      const response = await fetch(url, {
        headers: {
          'Content-Type': 'application/json',
          ...options.headers
        },
        ...options
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }
      
      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }
  
  static async get(url) {
    return this.request(url);
  }
  
  static async post(url, data) {
    return this.request(url, {
      method: 'POST',
      body: JSON.stringify(data)
    });
  }
  
  static async put(url, data) {
    return this.request(url, {
      method: 'PUT',
      body: JSON.stringify(data)
    });
  }
  
  static async delete(url) {
    return this.request(url, {
      method: 'DELETE'
    });
  }
  
  static async uploadFile(url, formData) {
    try {
      const response = await fetch(url, {
        method: 'POST',
        body: formData
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }
      
      return data;
    } catch (error) {
      console.error('File upload failed:', error);
      throw error;
    }
  }
}

// Books API
class BooksAPI {
  static async getAll() {
    return API.get('/api/books');
  }
  
  static async getById(id) {
    return API.get(`/api/books/${id}`);
  }
  
  static async create(bookData) {
    return API.post('/api/books', bookData);
  }
  
  static async update(id, bookData) {
    return API.put(`/api/books/${id}`, bookData);
  }
  
  static async delete(id) {
    return API.delete(`/api/books/${id}`);
  }
  
  static async search(searchTerm) {
    const books = await this.getAll();
    return books.filter(book => 
      book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (book.isbn && book.isbn.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (book.category && book.category.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }
  
  static async getAvailable() {
    const books = await this.getAll();
    return books.filter(book => book.available_copies > 0 && book.status === 'available');
  }
}

// Users API
class UsersAPI {
  static async getAll() {
    return API.get('/api/users');
  }
  
  static async getById(id) {
    return API.get(`/api/users/${id}`);
  }
  
  static async getProfile() {
    return API.get('/api/users/profile');
  }
  
  static async create(userData) {
    return API.post('/api/users', userData);
  }
  
  static async update(id, userData) {
    return API.put(`/api/users/${id}`, userData);
  }
  
  static async delete(id) {
    return API.delete(`/api/users/${id}`);
  }
  
  static async getStudents() {
    return API.get('/api/users/students/list');
  }
  
  static async search(searchTerm) {
    const users = await this.getAll();
    return users.filter(user =>
      user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.student_id && user.student_id.toLowerCase().includes(searchTerm.toLowerCase()))
    );
  }

  static async softDelete(id) {
    return API.put(`/api/users/${id}/soft-delete`);
  }

  static async restore(id) {
    return API.put(`/api/users/${id}/restore`);
  }

  static async blockUser(id) {
    return API.put(`/api/users/${id}/block`);
  }

  static async unblockUser(id) {
    return API.put(`/api/users/${id}/unblock`);
  }
}

// Issues API
class IssuesAPI {
  static async getAll() {
    return API.get('/api/issues');
  }
  
  static async getById(id) {
    return API.get(`/api/issues/${id}`);
  }
  
  static async getActive() {
    return API.get('/api/issues/active');
  }
  
  static async getOverdue() {
    return API.get('/api/issues/overdue');
  }
  
  static async getByUserId(userId) {
    return API.get(`/api/issues/user/${userId}`);
  }
  
  static async getMyIssues() {
    return API.get('/api/issues/my-issues');
  }
  
  static async create(issueData) {
    return API.post('/api/issues', issueData);
  }
  
  static async returnBook(id) {
    return API.put(`/api/issues/${id}/return`, {});
  }
  
  static async updateFine(id, fineAmount) {
    return API.put(`/api/issues/${id}/fine`, { fine_amount: fineAmount });
  }
  
  static async delete(id) {
    return API.delete(`/api/issues/${id}`);
  }
  
  static async getStatistics() {
    return API.get('/api/issues/stats/summary');
  }
}

// Auth API
class AuthAPI {
  static async login(email, password) {
    return API.post('/api/auth/login', { email, password });
  }
  
  static async logout() {
    return API.post('/api/auth/logout', {});
  }
  
  static async getStatus() {
    return API.get('/api/auth/status');
  }
  
  static async changePassword(currentPassword, newPassword) {
    return API.post('/api/auth/change-password', {
      currentPassword,
      newPassword
    });
  }
}

// File Upload API
class FileAPI {
  static async uploadCover(file) {
    const formData = new FormData();
    formData.append('cover', file);
    return API.uploadFile('/api/upload', formData);
  }
}

// Utility Functions
class Utils {
  static formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }
  
  static formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
  
  static calculateDaysOverdue(dueDateString) {
    const dueDate = new Date(dueDateString);
    const today = new Date();
    const diffTime = today - dueDate;
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  }
  
  static generateISBN() {
    // Generate a simple ISBN-like number for demo purposes
    const prefix = '978';
    const group = Math.floor(Math.random() * 10);
    const publisher = Math.floor(Math.random() * 100000).toString().padStart(5, '0');
    const title = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    const check = Math.floor(Math.random() * 10);
    
    return `${prefix}-${group}-${publisher}-${title}-${check}`;
  }
  
  static validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }
  
  static validateISBN(isbn) {
    // Basic ISBN validation (simplified)
    const cleanISBN = isbn.replace(/[-\s]/g, '');
    return cleanISBN.length === 10 || cleanISBN.length === 13;
  }
  
  static showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
      </div>
      <button class="notification-close" onclick="this.parentElement.remove()">
        <i class="fas fa-times"></i>
      </button>
    `;
    
    // Add to page
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      if (notification.parentElement) {
        notification.remove();
      }
    }, 5000);
  }
  
  static showConfirm(message, onConfirm, onCancel = null) {
    const modal = document.createElement('div');
    modal.className = 'modal confirm-modal';
    modal.innerHTML = `
      <div class="modal-content">
        <div class="modal-header">
          <i class="fas fa-question-circle"></i>
          <h3>Confirm Action</h3>
        </div>
        <div class="modal-body">
          <p>${message}</p>
        </div>
        <div class="modal-footer">
          <button class="btn btn-secondary" onclick="closeConfirmModal()">Cancel</button>
          <button class="btn btn-danger" onclick="confirmAction()">Confirm</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(modal);
    modal.style.display = 'flex';
    
    // Add event handlers
    window.closeConfirmModal = () => {
      modal.remove();
      if (onCancel) onCancel();
    };
    
    window.confirmAction = () => {
      modal.remove();
      onConfirm();
    };
    
    // Close on outside click
    modal.addEventListener('click', (e) => {
      if (e.target === modal) {
        window.closeConfirmModal();
      }
    });
  }
}

// Export for global use
window.API = API;
window.BooksAPI = BooksAPI;
window.UsersAPI = UsersAPI;
window.IssuesAPI = IssuesAPI;
window.AuthAPI = AuthAPI;
window.FileAPI = FileAPI;
window.Utils = Utils;
