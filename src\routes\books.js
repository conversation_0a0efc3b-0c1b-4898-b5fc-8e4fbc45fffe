const express = require('express');
const Book = require('../models/Book');
const router = express.Router();

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
  if (!req.session.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (!req.session.user || req.session.user.user_type !== 'library_admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

// Get all books (accessible to all authenticated users)
router.get('/', requireAuth, (req, res) => {
  Book.getAll((err, books) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(books);
  });
});

// Get a single book (accessible to all authenticated users)
router.get('/:id', requireAuth, (req, res) => {
  Book.getById(req.params.id, (err, book) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!book) {
      return res.status(404).json({ error: 'Book not found' });
    }
    res.json(book);
  });
});

// Create a new book (admin only)
router.post('/', requireAuth, requireAdmin, (req, res) => {
  console.log('Creating book with data:', req.body);

  // Validate required fields
  const { title, author } = req.body;
  if (!title || !author) {
    return res.status(400).json({ error: 'Title and Author are required' });
  }

  Book.create(req.body, (err, book) => {
    if (err) {
      console.error('Book creation error:', err);
      return res.status(500).json({ error: err.message });
    }
    console.log('Book created successfully:', book);
    res.status(201).json(book);
  });
});

// Update a book (admin only)
router.put('/:id', requireAuth, requireAdmin, (req, res) => {
  console.log('Updating book with data:', req.body);

  Book.update(req.params.id, req.body, (err) => {
    if (err) {
      console.error('Book update error:', err);
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Book updated successfully' });
  });
});

// Delete a book (admin only)
router.delete('/:id', requireAuth, requireAdmin, (req, res) => {
  Book.delete(req.params.id, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Book deleted successfully' });
  });
});

module.exports = router;