<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Library Management System - Dashboard</title>
  <link rel="stylesheet" href="css/dashboard.css">
  <link rel="stylesheet" href="css/modals.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
  <!-- Navigation Bar -->
  <nav class="navbar">
    <div class="nav-container">
      <div class="nav-brand">
        <i class="fas fa-book-open"></i>
        <span>Library Management</span>
      </div>
      
      <div class="nav-menu" id="navMenu">
        <a href="#" class="nav-link active" data-section="dashboard">
          <i class="fas fa-tachometer-alt"></i>
          <span>Dashboard</span>
        </a>
        <a href="#" class="nav-link" data-section="books">
          <i class="fas fa-book"></i>
          <span>Books</span>
        </a>
        <a href="#" class="nav-link" data-section="catalog">
          <i class="fas fa-list"></i>
          <span>Catalog</span>
        </a>
        <a href="#" class="nav-link admin-only" data-section="users">
          <i class="fas fa-users"></i>
          <span>Members</span>
        </a>
        <a href="#" class="nav-link admin-only" data-section="issues">
          <i class="fas fa-exchange-alt"></i>
          <span>Circulation</span>
        </a>
        <a href="#" class="nav-link" data-section="reservations">
          <i class="fas fa-bookmark"></i>
          <span>Reservations</span>
        </a>
        <a href="#" class="nav-link admin-only" data-section="fines">
          <i class="fas fa-dollar-sign"></i>
          <span>Fines</span>
        </a>
        <a href="#" class="nav-link admin-only" data-section="acquisitions">
          <i class="fas fa-shopping-cart"></i>
          <span>Acquisitions</span>
        </a>
        <a href="#" class="nav-link admin-only" data-section="reports">
          <i class="fas fa-chart-bar"></i>
          <span>Reports</span>
        </a>
      </div>
      
      <div class="nav-user">
        <div class="user-info" onclick="toggleUserMenu()">
          <div class="user-avatar">
            <i class="fas fa-user"></i>
          </div>
          <div class="user-details">
            <span class="user-name" id="userName">Loading...</span>
            <span class="user-role" id="userRole">Loading...</span>
          </div>
          <i class="fas fa-chevron-down"></i>
        </div>
        
        <div class="user-menu" id="userMenu">
          <a href="#" onclick="showProfile()">
            <i class="fas fa-user-edit"></i>
            Profile
          </a>
          <a href="#" onclick="showChangePassword()">
            <i class="fas fa-key"></i>
            Change Password
          </a>
          <div class="menu-divider"></div>
          <a href="#" onclick="logout()">
            <i class="fas fa-sign-out-alt"></i>
            Logout
          </a>
        </div>
      </div>
      
      <div class="nav-toggle" onclick="toggleMobileMenu()">
        <span></span>
        <span></span>
        <span></span>
      </div>
    </div>
  </nav>

  <!-- Main Content -->
  <main class="main-content">
    <!-- Dashboard Section -->
    <section id="dashboardSection" class="content-section active">
      <div class="section-header">
        <h1>Dashboard</h1>
        <p>Welcome to the Library Management System</p>
      </div>
      
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon books">
            <i class="fas fa-book"></i>
          </div>
          <div class="stat-info">
            <h3 id="totalBooks">0</h3>
            <p>Total Books</p>
          </div>
        </div>
        
        <div class="stat-card admin-only">
          <div class="stat-icon users">
            <i class="fas fa-users"></i>
          </div>
          <div class="stat-info">
            <h3 id="totalUsers">0</h3>
            <p>Total Users</p>
          </div>
        </div>
        
        <div class="stat-card admin-only">
          <div class="stat-icon issued">
            <i class="fas fa-hand-holding"></i>
          </div>
          <div class="stat-info">
            <h3 id="issuedBooks">0</h3>
            <p>Books Issued</p>
          </div>
        </div>
        
        <div class="stat-card admin-only">
          <div class="stat-icon overdue">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <div class="stat-info">
            <h3 id="overdueBooks">0</h3>
            <p>Overdue Books</p>
          </div>
        </div>
      </div>
      
      <div class="dashboard-content">
        <div class="recent-activities">
          <h2>Recent Activities</h2>
          <div class="activity-list" id="recentActivities">
            <!-- Activities will be loaded here -->
          </div>
        </div>
        
        <div class="quick-actions admin-only">
          <h2>Quick Actions</h2>
          <div class="action-buttons">
            <button class="action-btn" onclick="showAddBookModal()">
              <i class="fas fa-plus"></i>
              Add New Book
            </button>
            <button class="action-btn" onclick="showAddUserModal()">
              <i class="fas fa-user-plus"></i>
              Add New User
            </button>
            <button class="action-btn" onclick="showIssueBookModal()">
              <i class="fas fa-hand-holding"></i>
              Issue Book
            </button>
          </div>
        </div>
      </div>
    </section>

    <!-- Books Section -->
    <section id="booksSection" class="content-section">
      <div class="section-header">
        <h1>Books Management</h1>
        <div class="header-actions">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="bookSearch" placeholder="Search books...">
          </div>
          <button class="btn btn-primary admin-only" onclick="showAddBookModal()">
            <i class="fas fa-plus"></i>
            Add Book
          </button>
        </div>
      </div>
      
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>Cover</th>
              <th>Title</th>
              <th>Author</th>
              <th>ISBN</th>
              <th>Category</th>
              <th>Available/Total</th>
              <th>Status</th>
              <th class="admin-only">Actions</th>
            </tr>
          </thead>
          <tbody id="booksTableBody">
            <!-- Books will be loaded here -->
          </tbody>
        </table>
      </div>
    </section>

    <!-- Users Section (Admin Only) -->
    <section id="usersSection" class="content-section admin-only">
      <div class="section-header">
        <h1>Users Management</h1>
        <div class="header-actions">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="userSearch" placeholder="Search users...">
          </div>
          <button class="btn btn-primary" onclick="showAddUserModal()">
            <i class="fas fa-user-plus"></i>
            Add User
          </button>
        </div>
      </div>
      
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>Name</th>
              <th>Email</th>
              <th>Phone</th>
              <th>Type</th>
              <th>Student ID</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="usersTableBody">
            <!-- Users will be loaded here -->
          </tbody>
        </table>
      </div>
    </section>

    <!-- Issues Section (Admin Only) -->
    <section id="issuesSection" class="content-section admin-only">
      <div class="section-header">
        <h1>Book Issues Management</h1>
        <div class="header-actions">
          <div class="filter-tabs">
            <button class="filter-tab active" data-filter="all">All Issues</button>
            <button class="filter-tab" data-filter="active">Active</button>
            <button class="filter-tab" data-filter="overdue">Overdue</button>
            <button class="filter-tab" data-filter="returned">Returned</button>
          </div>
          <button class="btn btn-primary" onclick="showIssueBookModal()">
            <i class="fas fa-hand-holding"></i>
            Issue Book
          </button>
        </div>
      </div>
      
      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>Book</th>
              <th>Student</th>
              <th>Issue Date</th>
              <th>Due Date</th>
              <th>Return Date</th>
              <th>Days Issued</th>
              <th>Status</th>
              <th>Fine</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="issuesTableBody">
            <!-- Issues will be loaded here -->
          </tbody>
        </table>
      </div>
    </section>

    <!-- Catalog Section -->
    <section id="catalogSection" class="content-section">
      <div class="section-header">
        <h1>Library Catalog</h1>
        <p>Browse and search the complete library collection</p>
        <div class="header-actions">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="catalogSearch" placeholder="Search catalog...">
          </div>
          <select id="categoryFilter" class="filter-select">
            <option value="">All Categories</option>
            <option value="Fiction">Fiction</option>
            <option value="Non-Fiction">Non-Fiction</option>
            <option value="Science">Science</option>
            <option value="Technology">Technology</option>
            <option value="History">History</option>
            <option value="Biography">Biography</option>
          </select>
          <select id="availabilityFilter" class="filter-select">
            <option value="">All Books</option>
            <option value="available">Available Only</option>
            <option value="unavailable">Unavailable Only</option>
          </select>
        </div>
      </div>

      <div class="catalog-grid" id="catalogGrid">
        <!-- Catalog items will be loaded here -->
      </div>
    </section>

    <!-- Reservations Section -->
    <section id="reservationsSection" class="content-section">
      <div class="section-header">
        <h1>Book Reservations</h1>
        <p>Manage book reservations and holds</p>
        <div class="header-actions">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="reservationSearch" placeholder="Search reservations...">
          </div>
          <button class="btn btn-primary" onclick="showReserveBookModal()">
            <i class="fas fa-bookmark"></i>
            Reserve Book
          </button>
        </div>
      </div>

      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>Book</th>
              <th>Member</th>
              <th>Reserved Date</th>
              <th>Status</th>
              <th>Priority</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="reservationsTableBody">
            <!-- Reservations will be loaded here -->
          </tbody>
        </table>
      </div>
    </section>

    <!-- Fines Section (Admin Only) -->
    <section id="finesSection" class="content-section admin-only">
      <div class="section-header">
        <h1>Fines Management</h1>
        <p>Track and manage library fines</p>
        <div class="header-actions">
          <div class="filter-tabs">
            <button class="filter-tab active" data-filter="all">All Fines</button>
            <button class="filter-tab" data-filter="pending">Pending</button>
            <button class="filter-tab" data-filter="paid">Paid</button>
            <button class="filter-tab" data-filter="waived">Waived</button>
          </div>
        </div>
      </div>

      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>Member</th>
              <th>Book</th>
              <th>Fine Amount</th>
              <th>Reason</th>
              <th>Date Issued</th>
              <th>Status</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="finesTableBody">
            <!-- Fines will be loaded here -->
          </tbody>
        </table>
      </div>
    </section>

    <!-- Acquisitions Section (Admin Only) -->
    <section id="acquisitionsSection" class="content-section admin-only">
      <div class="section-header">
        <h1>Book Acquisitions</h1>
        <p>Manage book procurement and cost estimation</p>
        <div class="header-actions">
          <div class="search-box">
            <i class="fas fa-search"></i>
            <input type="text" id="acquisitionSearch" placeholder="Search requests...">
          </div>
          <button class="btn btn-primary" onclick="showAcquisitionRequestModal()">
            <i class="fas fa-plus"></i>
            New Request
          </button>
        </div>
      </div>

      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon books">
            <i class="fas fa-shopping-cart"></i>
          </div>
          <div class="stat-info">
            <h3 id="totalRequests">0</h3>
            <p>Total Requests</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon users">
            <i class="fas fa-clock"></i>
          </div>
          <div class="stat-info">
            <h3 id="pendingRequests">0</h3>
            <p>Pending Requests</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon issued">
            <i class="fas fa-dollar-sign"></i>
          </div>
          <div class="stat-info">
            <h3 id="estimatedCost">$0</h3>
            <p>Estimated Cost</p>
          </div>
        </div>

        <div class="stat-card">
          <div class="stat-icon overdue">
            <i class="fas fa-check"></i>
          </div>
          <div class="stat-info">
            <h3 id="approvedRequests">0</h3>
            <p>Approved</p>
          </div>
        </div>
      </div>

      <div class="table-container">
        <table class="data-table">
          <thead>
            <tr>
              <th>Title</th>
              <th>Author</th>
              <th>ISBN</th>
              <th>Estimated Cost</th>
              <th>Priority</th>
              <th>Status</th>
              <th>Requested By</th>
              <th>Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody id="acquisitionsTableBody">
            <!-- Acquisition requests will be loaded here -->
          </tbody>
        </table>
      </div>
    </section>

    <!-- Reports Section (Admin Only) -->
    <section id="reportsSection" class="content-section admin-only">
      <div class="section-header">
        <h1>Reports & Analytics</h1>
        <p>Generate comprehensive library reports and analytics</p>
      </div>

      <!-- Report Type Tabs -->
      <div class="report-tabs">
        <button class="report-tab active" data-report="summary" onclick="switchReportTab(this, 'summary')">
          <i class="fas fa-chart-pie"></i> Summary
        </button>
        <button class="report-tab" data-report="users" onclick="switchReportTab(this, 'users')">
          <i class="fas fa-users"></i> User Reports
        </button>
        <button class="report-tab" data-report="books" onclick="switchReportTab(this, 'books')">
          <i class="fas fa-book"></i> Book Reports
        </button>
        <button class="report-tab" data-report="issues" onclick="switchReportTab(this, 'issues')">
          <i class="fas fa-exchange-alt"></i> Issue Reports
        </button>
        <button class="report-tab" data-report="analytics" onclick="switchReportTab(this, 'analytics')">
          <i class="fas fa-chart-line"></i> Analytics
        </button>
      </div>

      <!-- Summary Report -->
      <div id="summaryReport" class="report-content active">
        <div class="summary-stats">
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-users"></i>
            </div>
            <div class="stat-info">
              <h3 id="totalUsers">-</h3>
              <p>Total Users</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-book"></i>
            </div>
            <div class="stat-info">
              <h3 id="totalBooks">-</h3>
              <p>Total Books</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-exchange-alt"></i>
            </div>
            <div class="stat-info">
              <h3 id="totalIssues">-</h3>
              <p>Total Issues</p>
            </div>
          </div>
          <div class="stat-card">
            <div class="stat-icon">
              <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="stat-info">
              <h3 id="overdueIssues">-</h3>
              <p>Overdue Issues</p>
            </div>
          </div>
        </div>

        <div class="summary-charts">
          <div class="chart-card">
            <h3>User Distribution</h3>
            <div id="userDistributionChart" class="chart-content">
              <!-- User distribution chart -->
            </div>
          </div>
          <div class="chart-card">
            <h3>Book Status</h3>
            <div id="bookStatusChart" class="chart-content">
              <!-- Book status chart -->
            </div>
          </div>
        </div>
      </div>

      <!-- User Reports -->
      <div id="userReport" class="report-content">
        <div class="report-controls">
          <div class="control-group">
            <label>Export Format:</label>
            <select id="userReportFormat">
              <option value="table">Table View</option>
              <option value="csv">CSV Export</option>
              <option value="pdf">PDF Export</option>
            </select>
          </div>
          <button class="btn btn-primary" onclick="generateUserReport()">
            <i class="fas fa-file-alt"></i> Generate Report
          </button>
        </div>
        <div id="userReportTable" class="report-table-container">
          <!-- User report table will be loaded here -->
        </div>
      </div>

      <!-- Book Reports -->
      <div id="bookReport" class="report-content">
        <div class="report-controls">
          <div class="control-group">
            <label>Filter by Category:</label>
            <select id="bookCategoryFilter">
              <option value="">All Categories</option>
            </select>
          </div>
          <div class="control-group">
            <label>Export Format:</label>
            <select id="bookReportFormat">
              <option value="table">Table View</option>
              <option value="csv">CSV Export</option>
              <option value="pdf">PDF Export</option>
            </select>
          </div>
          <button class="btn btn-primary" onclick="generateBookReport()">
            <i class="fas fa-file-alt"></i> Generate Report
          </button>
        </div>
        <div id="bookReportTable" class="report-table-container">
          <!-- Book report table will be loaded here -->
        </div>
      </div>

      <!-- Issue Reports -->
      <div id="issueReport" class="report-content">
        <div class="report-controls">
          <div class="control-group">
            <label>Start Date:</label>
            <input type="date" id="issueStartDate">
          </div>
          <div class="control-group">
            <label>End Date:</label>
            <input type="date" id="issueEndDate">
          </div>
          <div class="control-group">
            <label>Status:</label>
            <select id="issueStatusFilter">
              <option value="">All Status</option>
              <option value="issued">Issued</option>
              <option value="returned">Returned</option>
            </select>
          </div>
          <div class="control-group">
            <label>Export Format:</label>
            <select id="issueReportFormat">
              <option value="table">Table View</option>
              <option value="csv">CSV Export</option>
              <option value="pdf">PDF Export</option>
            </select>
          </div>
          <button class="btn btn-primary" onclick="generateIssueReport()">
            <i class="fas fa-file-alt"></i> Generate Report
          </button>
        </div>
        <div id="issueReportTable" class="report-table-container">
          <!-- Issue report table will be loaded here -->
        </div>
      </div>

      <!-- Analytics -->
      <div id="analyticsReport" class="report-content">
        <div class="analytics-grid">
          <div class="analytics-card">
            <h3>Popular Books</h3>
            <div id="popularBooksChart" class="analytics-content">
              <!-- Popular books chart -->
            </div>
          </div>
          <div class="analytics-card">
            <h3>Overdue Analysis</h3>
            <div id="overdueAnalysisChart" class="analytics-content">
              <!-- Overdue analysis chart -->
            </div>
          </div>
          <div class="analytics-card">
            <h3>Issue Trends</h3>
            <div id="issueTrendsChart" class="analytics-content">
              <!-- Issue trends chart -->
            </div>
          </div>
          <div class="analytics-card">
            <h3>User Activity</h3>
            <div id="userActivityChart" class="analytics-content">
              <!-- User activity chart -->
            </div>
          </div>
        </div>
      </div>
    </section>
  </main>

  <!-- Include Modal Components -->
  <div id="modalContainer"></div>

  <!-- Loading Overlay -->
  <div id="loadingOverlay" class="loading-overlay">
    <div class="loading-spinner">
      <i class="fas fa-spinner fa-spin"></i>
      <p>Loading...</p>
    </div>
  </div>

  <!-- Scripts -->
  <script src="js/api.js"></script>
  <script src="js/dashboard.js"></script>
  <script src="js/modals.js"></script>
</body>
</html>
