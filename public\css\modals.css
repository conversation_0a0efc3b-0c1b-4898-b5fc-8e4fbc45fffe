/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  padding: 20px;
}

.modal.show {
  display: flex;
}

.modal-content {
  background: white;
  border-radius: 15px;
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  padding: 25px 30px 20px;
  border-bottom: 2px solid #e1e8ed;
  display: flex;
  align-items: center;
  gap: 15px;
}

.modal-header i {
  font-size: 1.5rem;
  color: #3498db;
}

.modal-header h3 {
  color: #2c3e50;
  margin: 0;
  flex: 1;
  font-size: 1.4rem;
  font-weight: 600;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  color: #7f8c8d;
  cursor: pointer;
  padding: 5px;
  line-height: 1;
  transition: color 0.3s ease;
  border-radius: 50%;
  width: 35px;
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close:hover {
  color: #e74c3c;
  background: #fadbd8;
}

.modal-body {
  padding: 25px 30px;
}

.modal-footer {
  padding: 20px 30px;
  border-top: 2px solid #e1e8ed;
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

/* Form Styles in Modals */
.modal-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
}

.form-group label i {
  color: #3498db;
  width: 16px;
}

.form-group label .required {
  color: #e74c3c;
  margin-left: 4px;
}

.form-input {
  padding: 12px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: #f8f9fa;
}

.form-input:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-input.error {
  border-color: #e74c3c;
  background: #fadbd8;
}

.form-select {
  padding: 12px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.form-select:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.form-textarea {
  padding: 12px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background: #f8f9fa;
  resize: vertical;
  min-height: 100px;
  font-family: inherit;
  transition: all 0.3s ease;
}

.form-textarea:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.error-message {
  color: #e74c3c;
  font-size: 0.85rem;
  margin-top: 5px;
  display: none;
}

.error-message.show {
  display: block;
}

/* File Upload Styles */
.file-upload {
  position: relative;
  display: inline-block;
  width: 100%;
}

.file-upload-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.file-upload-label {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  padding: 40px 20px;
  border: 2px dashed #e1e8ed;
  border-radius: 8px;
  background: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  color: #7f8c8d;
}

.file-upload-label:hover,
.file-upload.dragover .file-upload-label {
  border-color: #3498db;
  background: #ebf3fd;
  color: #3498db;
}

.file-upload-preview {
  margin-top: 15px;
  text-align: center;
}

.file-upload-preview img {
  max-width: 150px;
  max-height: 150px;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Search and Select Components */
.search-select {
  position: relative;
}

.search-select-input {
  width: 100%;
  padding: 12px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.search-select-input:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

.search-select-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 2px solid #e1e8ed;
  border-top: none;
  border-radius: 0 0 8px 8px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
}

.search-select-dropdown.show {
  display: block;
}

.search-select-option {
  padding: 12px 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid #f8f9fa;
  font-size: 0.95rem;
  line-height: 1.4;
}

.search-select-option:hover,
.search-select-option.highlighted {
  background: #ebf3fd;
  color: #3498db;
  transform: translateX(2px);
}

.search-select-option:last-child {
  border-bottom: none;
}

.search-select-option.no-results {
  color: #7f8c8d;
  font-style: italic;
  cursor: default;
  text-align: center;
}

.search-select-option.no-results:hover {
  background: transparent;
  color: #7f8c8d;
  transform: none;
}

.search-select-option.more-results {
  color: #7f8c8d;
  font-size: 0.85rem;
  cursor: default;
  text-align: center;
  font-style: italic;
  background: #f8f9fa;
}

.search-select-option.more-results:hover {
  background: #f8f9fa;
  color: #7f8c8d;
  transform: none;
}

/* Enhanced dropdown styling */
.search-select-dropdown {
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  border-color: #3498db;
}

.search-select-dropdown::-webkit-scrollbar {
  width: 6px;
}

.search-select-dropdown::-webkit-scrollbar-track {
  background: #f8f9fa;
}

.search-select-dropdown::-webkit-scrollbar-thumb {
  background: #bdc3c7;
  border-radius: 3px;
}

.search-select-dropdown::-webkit-scrollbar-thumb:hover {
  background: #95a5a6;
}

/* Date Input Styles */
.date-input {
  padding: 12px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  background: #f8f9fa;
  transition: all 0.3s ease;
  width: 100%;
}

.date-input:focus {
  outline: none;
  border-color: #3498db;
  background: white;
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
}

/* Checkbox and Radio Styles */
.checkbox-group,
.radio-group {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.checkbox-item,
.radio-item {
  display: flex;
  align-items: center;
  gap: 10px;
  cursor: pointer;
}

.checkbox-item input,
.radio-item input {
  width: auto;
  margin: 0;
}

/* Modal Specific Styles */
.add-book-modal .modal-content {
  max-width: 700px;
}

.add-user-modal .modal-content {
  max-width: 600px;
}

.issue-book-modal .modal-content {
  max-width: 650px;
}

.profile-modal .modal-content {
  max-width: 500px;
}

.change-password-modal .modal-content {
  max-width: 450px;
}

/* Success and Error States */
.form-success {
  background: #d5f4e6;
  color: #27ae60;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-error {
  background: #fadbd8;
  color: #e74c3c;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Loading States */
.modal-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: #7f8c8d;
}

.modal-loading i {
  font-size: 2rem;
  margin-bottom: 15px;
  color: #3498db;
}

/* Responsive Modal Design */
@media (max-width: 768px) {
  .modal {
    padding: 10px;
  }
  
  .modal-content {
    max-height: 95vh;
  }
  
  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 20px;
    padding-right: 20px;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 15px;
  }
  
  .modal-footer {
    flex-direction: column-reverse;
  }
  
  .modal-footer .btn {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .modal-header,
  .modal-body,
  .modal-footer {
    padding-left: 15px;
    padding-right: 15px;
  }
  
  .modal-header {
    padding-top: 20px;
    padding-bottom: 15px;
  }
  
  .modal-body {
    padding-top: 20px;
    padding-bottom: 20px;
  }
  
  .modal-footer {
    padding-top: 15px;
    padding-bottom: 20px;
  }
}
