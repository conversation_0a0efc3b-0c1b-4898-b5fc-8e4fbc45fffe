const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./library.db');

// Create acquisitions table
db.run(`
  CREATE TABLE IF NOT EXISTS acquisitions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT NOT NULL,
    author TEXT NOT NULL,
    isbn TEXT,
    publisher TEXT,
    publication_year INTEGER,
    category TEXT,
    estimated_cost DECIMAL(10,2),
    actual_cost DECIMAL(10,2),
    quantity_requested INTEGER DEFAULT 1,
    quantity_approved INTEGER DEFAULT 0,
    priority TEXT DEFAULT 'medium' CHECK(priority IN ('low', 'medium', 'high', 'urgent')),
    status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'approved', 'ordered', 'received', 'rejected', 'cancelled')),
    justification TEXT,
    requested_by INTEGER NOT NULL,
    approved_by INTEGER,
    vendor TEXT,
    order_date DATETIME,
    expected_delivery DATETIME,
    received_date DATETIME,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (requested_by) REFERENCES users (id),
    FOREIGN KEY (approved_by) REFERENCES users (id)
  )
`);

class Acquisition {
  static getAll(callback) {
    const query = `
      SELECT a.*, 
             u1.full_name as requested_by_name,
             u2.full_name as approved_by_name
      FROM acquisitions a
      JOIN users u1 ON a.requested_by = u1.id
      LEFT JOIN users u2 ON a.approved_by = u2.id
      ORDER BY 
        CASE a.priority 
          WHEN 'urgent' THEN 1 
          WHEN 'high' THEN 2 
          WHEN 'medium' THEN 3 
          WHEN 'low' THEN 4 
        END,
        a.created_at DESC
    `;
    
    db.all(query, callback);
  }

  static getById(id, callback) {
    const query = `
      SELECT a.*, 
             u1.full_name as requested_by_name, u1.email as requested_by_email,
             u2.full_name as approved_by_name, u2.email as approved_by_email
      FROM acquisitions a
      JOIN users u1 ON a.requested_by = u1.id
      LEFT JOIN users u2 ON a.approved_by = u2.id
      WHERE a.id = ?
    `;
    
    db.get(query, [id], callback);
  }

  static getByUser(userId, callback) {
    const query = `
      SELECT a.*, 
             u2.full_name as approved_by_name
      FROM acquisitions a
      LEFT JOIN users u2 ON a.approved_by = u2.id
      WHERE a.requested_by = ?
      ORDER BY a.created_at DESC
    `;
    
    db.all(query, [userId], callback);
  }

  static create(acquisition, callback) {
    const {
      title, author, isbn, publisher, publication_year, category,
      estimated_cost, quantity_requested, priority, justification,
      requested_by, vendor, expected_delivery, notes
    } = acquisition;
    
    db.run(
      `INSERT INTO acquisitions (
        title, author, isbn, publisher, publication_year, category,
        estimated_cost, quantity_requested, priority, justification,
        requested_by, vendor, expected_delivery, notes
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        title, author, isbn, publisher, publication_year, category,
        estimated_cost, quantity_requested || 1, priority || 'medium',
        justification, requested_by, vendor, expected_delivery, notes
      ],
      function(err) {
        if (err) return callback(err);
        callback(null, { id: this.lastID, ...acquisition });
      }
    );
  }

  static update(id, acquisition, callback) {
    const {
      title, author, isbn, publisher, publication_year, category,
      estimated_cost, actual_cost, quantity_requested, quantity_approved,
      priority, status, justification, vendor, order_date,
      expected_delivery, received_date, notes
    } = acquisition;
    
    const updatedAt = new Date().toISOString();
    
    db.run(
      `UPDATE acquisitions SET 
        title = ?, author = ?, isbn = ?, publisher = ?, publication_year = ?,
        category = ?, estimated_cost = ?, actual_cost = ?, quantity_requested = ?,
        quantity_approved = ?, priority = ?, status = ?, justification = ?,
        vendor = ?, order_date = ?, expected_delivery = ?, received_date = ?,
        notes = ?, updated_at = ?
      WHERE id = ?`,
      [
        title, author, isbn, publisher, publication_year, category,
        estimated_cost, actual_cost, quantity_requested, quantity_approved,
        priority, status, justification, vendor, order_date,
        expected_delivery, received_date, notes, updatedAt, id
      ],
      callback
    );
  }

  static approve(id, approvedBy, quantityApproved, callback) {
    const updatedAt = new Date().toISOString();
    
    db.run(
      'UPDATE acquisitions SET status = "approved", approved_by = ?, quantity_approved = ?, updated_at = ? WHERE id = ?',
      ['approved', approvedBy, quantityApproved, updatedAt, id],
      callback
    );
  }

  static reject(id, notes, callback) {
    const updatedAt = new Date().toISOString();
    
    db.run(
      'UPDATE acquisitions SET status = "rejected", notes = ?, updated_at = ? WHERE id = ?',
      ['rejected', notes, updatedAt, id],
      callback
    );
  }

  static markOrdered(id, vendor, orderDate, actualCost, callback) {
    const updatedAt = new Date().toISOString();
    
    db.run(
      'UPDATE acquisitions SET status = "ordered", vendor = ?, order_date = ?, actual_cost = ?, updated_at = ? WHERE id = ?',
      ['ordered', vendor, orderDate, actualCost, updatedAt, id],
      callback
    );
  }

  static markReceived(id, receivedDate, callback) {
    const updatedAt = new Date().toISOString();
    
    db.run(
      'UPDATE acquisitions SET status = "received", received_date = ?, updated_at = ? WHERE id = ?',
      ['received', receivedDate || new Date().toISOString(), updatedAt, id],
      callback
    );
  }

  static delete(id, callback) {
    db.run('DELETE FROM acquisitions WHERE id = ?', [id], callback);
  }

  static getStats(callback) {
    const query = `
      SELECT 
        COUNT(*) as total_requests,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'approved' THEN 1 END) as approved_count,
        COUNT(CASE WHEN status = 'ordered' THEN 1 END) as ordered_count,
        COUNT(CASE WHEN status = 'received' THEN 1 END) as received_count,
        COUNT(CASE WHEN status = 'rejected' THEN 1 END) as rejected_count,
        SUM(estimated_cost * quantity_requested) as total_estimated_cost,
        SUM(CASE WHEN status IN ('approved', 'ordered', 'received') THEN estimated_cost * quantity_approved END) as approved_estimated_cost,
        SUM(actual_cost * quantity_approved) as total_actual_cost,
        COUNT(CASE WHEN priority = 'urgent' THEN 1 END) as urgent_count,
        COUNT(CASE WHEN priority = 'high' THEN 1 END) as high_priority_count
      FROM acquisitions
    `;
    
    db.get(query, callback);
  }

  static getBudgetSummary(callback) {
    const query = `
      SELECT 
        SUM(CASE WHEN status = 'pending' THEN estimated_cost * quantity_requested END) as pending_budget,
        SUM(CASE WHEN status = 'approved' THEN estimated_cost * quantity_approved END) as approved_budget,
        SUM(CASE WHEN status = 'ordered' THEN actual_cost * quantity_approved END) as ordered_budget,
        SUM(CASE WHEN status = 'received' THEN actual_cost * quantity_approved END) as spent_budget
      FROM acquisitions
    `;
    
    db.get(query, callback);
  }

  static getPopularRequests(callback) {
    const query = `
      SELECT title, author, COUNT(*) as request_count
      FROM acquisitions
      GROUP BY title, author
      HAVING COUNT(*) > 1
      ORDER BY request_count DESC
      LIMIT 10
    `;
    
    db.all(query, callback);
  }
}

module.exports = Acquisition;
