const express = require('express');
const Acquisition = require('../models/Acquisition');
const router = express.Router();

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
  if (!req.session.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (!req.session.user || req.session.user.user_type !== 'library_admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

// Get all acquisition requests (admin only)
router.get('/', requireAuth, requireAdmin, (req, res) => {
  Acquisition.getAll((err, acquisitions) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(acquisitions);
  });
});

// Get user's acquisition requests
router.get('/my', requireAuth, (req, res) => {
  Acquisition.getByUser(req.session.user.id, (err, acquisitions) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(acquisitions);
  });
});

// Get a single acquisition request
router.get('/:id', requireAuth, (req, res) => {
  Acquisition.getById(req.params.id, (err, acquisition) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!acquisition) {
      return res.status(404).json({ error: 'Acquisition request not found' });
    }
    
    // Students can only view their own requests
    if (req.session.user.user_type === 'student' && acquisition.requested_by !== req.session.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    res.json(acquisition);
  });
});

// Create a new acquisition request
router.post('/', requireAuth, (req, res) => {
  const { title, author, isbn, publisher, publication_year, category, estimated_cost, quantity_requested, priority, justification, vendor, expected_delivery, notes } = req.body;
  
  if (!title || !author) {
    return res.status(400).json({ error: 'Title and Author are required' });
  }
  
  if (estimated_cost && estimated_cost <= 0) {
    return res.status(400).json({ error: 'Estimated cost must be greater than 0' });
  }
  
  const acquisitionData = {
    title,
    author,
    isbn,
    publisher,
    publication_year,
    category,
    estimated_cost,
    quantity_requested: quantity_requested || 1,
    priority: priority || 'medium',
    justification,
    requested_by: req.session.user.id,
    vendor,
    expected_delivery,
    notes
  };
  
  Acquisition.create(acquisitionData, (err, acquisition) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.status(201).json(acquisition);
  });
});

// Update an acquisition request
router.put('/:id', requireAuth, (req, res) => {
  // First check permissions
  Acquisition.getById(req.params.id, (err, acquisition) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!acquisition) {
      return res.status(404).json({ error: 'Acquisition request not found' });
    }
    
    // Students can only edit their own pending requests
    if (req.session.user.user_type === 'student') {
      if (acquisition.requested_by !== req.session.user.id) {
        return res.status(403).json({ error: 'Access denied' });
      }
      if (acquisition.status !== 'pending') {
        return res.status(403).json({ error: 'Cannot edit non-pending requests' });
      }
    }
    
    Acquisition.update(req.params.id, req.body, (err) => {
      if (err) {
        return res.status(500).json({ error: err.message });
      }
      res.json({ message: 'Acquisition request updated successfully' });
    });
  });
});

// Approve an acquisition request (admin only)
router.post('/:id/approve', requireAuth, requireAdmin, (req, res) => {
  const { quantity_approved } = req.body;
  
  if (!quantity_approved || quantity_approved <= 0) {
    return res.status(400).json({ error: 'Valid approved quantity is required' });
  }
  
  Acquisition.approve(req.params.id, req.session.user.id, quantity_approved, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Acquisition request approved successfully' });
  });
});

// Reject an acquisition request (admin only)
router.post('/:id/reject', requireAuth, requireAdmin, (req, res) => {
  const { notes } = req.body;
  
  Acquisition.reject(req.params.id, notes, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Acquisition request rejected' });
  });
});

// Mark as ordered (admin only)
router.post('/:id/order', requireAuth, requireAdmin, (req, res) => {
  const { vendor, order_date, actual_cost } = req.body;
  
  if (!vendor) {
    return res.status(400).json({ error: 'Vendor is required' });
  }
  
  Acquisition.markOrdered(req.params.id, vendor, order_date, actual_cost, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Acquisition marked as ordered successfully' });
  });
});

// Mark as received (admin only)
router.post('/:id/receive', requireAuth, requireAdmin, (req, res) => {
  const { received_date } = req.body;
  
  Acquisition.markReceived(req.params.id, received_date, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Acquisition marked as received successfully' });
  });
});

// Delete an acquisition request
router.delete('/:id', requireAuth, (req, res) => {
  // First check permissions
  Acquisition.getById(req.params.id, (err, acquisition) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!acquisition) {
      return res.status(404).json({ error: 'Acquisition request not found' });
    }
    
    // Students can only delete their own pending requests
    if (req.session.user.user_type === 'student') {
      if (acquisition.requested_by !== req.session.user.id) {
        return res.status(403).json({ error: 'Access denied' });
      }
      if (acquisition.status !== 'pending') {
        return res.status(403).json({ error: 'Cannot delete non-pending requests' });
      }
    }
    
    Acquisition.delete(req.params.id, (err) => {
      if (err) {
        return res.status(500).json({ error: err.message });
      }
      res.json({ message: 'Acquisition request deleted successfully' });
    });
  });
});

// Get acquisition statistics (admin only)
router.get('/stats/summary', requireAuth, requireAdmin, (req, res) => {
  Acquisition.getStats((err, stats) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(stats);
  });
});

// Get budget summary (admin only)
router.get('/stats/budget', requireAuth, requireAdmin, (req, res) => {
  Acquisition.getBudgetSummary((err, budget) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(budget);
  });
});

// Get popular requests (admin only)
router.get('/stats/popular', requireAuth, requireAdmin, (req, res) => {
  Acquisition.getPopularRequests((err, popular) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(popular);
  });
});

module.exports = router;
