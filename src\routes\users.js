const express = require('express');
const User = require('../models/User');
const router = express.Router();

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
  if (!req.session.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (!req.session.user || req.session.user.user_type !== 'library_admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

// Get all users (admin only)
router.get('/', requireAuth, requireAdmin, (req, res) => {
  User.getAll((err, users) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(users);
  });
});

// Get current user profile
router.get('/profile', requireAuth, (req, res) => {
  User.getById(req.session.user.id, (err, user) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    res.json(user);
  });
});

// Get a single user (admin only)
router.get('/:id', requireAuth, requireAdmin, (req, res) => {
  User.getById(req.params.id, (err, user) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }
    res.json(user);
  });
});

// Create a new user (admin only)
router.post('/', requireAuth, requireAdmin, (req, res) => {
  const { full_name, email, phone, password, user_type, student_id } = req.body;
  
  // Validation
  if (!full_name || !email || !password || !user_type) {
    return res.status(400).json({ error: 'Full name, email, password, and user type are required' });
  }
  
  if (user_type === 'student' && !student_id) {
    return res.status(400).json({ error: 'Student ID is required for student accounts' });
  }
  
  User.create(req.body, (err, user) => {
    if (err) {
      if (err.message.includes('UNIQUE constraint failed')) {
        return res.status(400).json({ error: 'Email or Student ID already exists' });
      }
      return res.status(500).json({ error: err.message });
    }
    res.status(201).json(user);
  });
});

// Update a user (admin only, or user updating their own profile)
router.put('/:id', requireAuth, (req, res) => {
  const userId = parseInt(req.params.id);
  const currentUser = req.session.user;
  
  // Check if user is admin or updating their own profile
  if (currentUser.user_type !== 'library_admin' && currentUser.id !== userId) {
    return res.status(403).json({ error: 'Access denied' });
  }
  
  // Students can only update certain fields
  if (currentUser.user_type === 'student' && currentUser.id === userId) {
    const allowedFields = ['full_name', 'phone'];
    const updateData = {};
    allowedFields.forEach(field => {
      if (req.body[field] !== undefined) {
        updateData[field] = req.body[field];
      }
    });
    updateData.email = currentUser.email; // Keep original email
    updateData.user_type = currentUser.user_type; // Keep original user type
    updateData.student_id = currentUser.student_id; // Keep original student ID
    updateData.is_active = 1; // Keep active
    
    User.update(userId, updateData, (err) => {
      if (err) {
        return res.status(500).json({ error: err.message });
      }
      res.json({ message: 'Profile updated successfully' });
    });
  } else {
    // Admin can update all fields
    // Validate required fields before updating
    const { full_name, email, user_type } = req.body;
    if (!full_name || !email || !user_type) {
      return res.status(400).json({ error: 'Full name, email, and user type are required' });
    }

    User.update(userId, req.body, (err) => {
      if (err) {
        if (err.message.includes('UNIQUE constraint failed')) {
          return res.status(400).json({ error: 'Email or Student ID already exists' });
        }
        return res.status(500).json({ error: err.message });
      }
      res.json({ message: 'User updated successfully' });
    });
  }
});

// Delete a user (admin only)
router.delete('/:id', requireAuth, requireAdmin, (req, res) => {
  const userId = parseInt(req.params.id);
  
  // Prevent admin from deleting themselves
  if (req.session.user.id === userId) {
    return res.status(400).json({ error: 'Cannot delete your own account' });
  }
  
  User.delete(userId, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'User deleted successfully' });
  });
});

// Get all students (for book issuing)
router.get('/students/list', requireAuth, requireAdmin, (req, res) => {
  User.getStudents((err, students) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(students);
  });
});

module.exports = router;
