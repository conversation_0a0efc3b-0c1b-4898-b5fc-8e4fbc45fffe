const express = require('express');
const Fine = require('../models/Fine');
const router = express.Router();

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
  if (!req.session.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (!req.session.user || req.session.user.user_type !== 'library_admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

// Get all fines (admin only)
router.get('/', requireAuth, requireAdmin, (req, res) => {
  Fine.getAll((err, fines) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(fines);
  });
});

// Get user's fines
router.get('/my', requireAuth, (req, res) => {
  Fine.getByUser(req.session.user.id, (err, fines) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(fines);
  });
});

// Get user's fines summary
router.get('/my/summary', requireAuth, (req, res) => {
  Fine.getUserFinesSummary(req.session.user.id, (err, summary) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(summary);
  });
});

// Get a single fine
router.get('/:id', requireAuth, (req, res) => {
  Fine.getById(req.params.id, (err, fine) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!fine) {
      return res.status(404).json({ error: 'Fine not found' });
    }
    
    // Students can only view their own fines
    if (req.session.user.user_type === 'student' && fine.user_id !== req.session.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    res.json(fine);
  });
});

// Create a new fine (admin only)
router.post('/', requireAuth, requireAdmin, (req, res) => {
  const { user_id, issue_id, book_id, amount, reason, notes } = req.body;
  
  if (!user_id || !amount || !reason) {
    return res.status(400).json({ error: 'User ID, amount, and reason are required' });
  }
  
  if (amount <= 0) {
    return res.status(400).json({ error: 'Fine amount must be greater than 0' });
  }
  
  Fine.create(req.body, (err, fine) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.status(201).json(fine);
  });
});

// Update a fine (admin only)
router.put('/:id', requireAuth, requireAdmin, (req, res) => {
  Fine.update(req.params.id, req.body, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Fine updated successfully' });
  });
});

// Pay a fine
router.post('/:id/pay', requireAuth, (req, res) => {
  const { amount } = req.body;
  
  if (!amount || amount <= 0) {
    return res.status(400).json({ error: 'Valid payment amount is required' });
  }
  
  // First check if user owns this fine or is admin
  Fine.getById(req.params.id, (err, fine) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!fine) {
      return res.status(404).json({ error: 'Fine not found' });
    }
    
    // Students can only pay their own fines
    if (req.session.user.user_type === 'student' && fine.user_id !== req.session.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    Fine.payFine(req.params.id, amount, (err) => {
      if (err) {
        return res.status(500).json({ error: err.message });
      }
      res.json({ message: 'Payment recorded successfully' });
    });
  });
});

// Waive a fine (admin only)
router.post('/:id/waive', requireAuth, requireAdmin, (req, res) => {
  Fine.waiveFine(req.params.id, req.session.user.id, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Fine waived successfully' });
  });
});

// Delete a fine (admin only)
router.delete('/:id', requireAuth, requireAdmin, (req, res) => {
  Fine.delete(req.params.id, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Fine deleted successfully' });
  });
});

// Get fine statistics (admin only)
router.get('/stats/summary', requireAuth, requireAdmin, (req, res) => {
  Fine.getStats((err, stats) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(stats);
  });
});

// Generate overdue fines (admin only)
router.post('/generate/overdue', requireAuth, requireAdmin, (req, res) => {
  Fine.generateOverdueFines((err, result) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ 
      message: `Generated ${result.generated} overdue fines`,
      generated: result.generated 
    });
  });
});

module.exports = router;
