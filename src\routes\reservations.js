const express = require('express');
const Reservation = require('../models/Reservation');
const router = express.Router();

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
  if (!req.session.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (!req.session.user || req.session.user.user_type !== 'library_admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

// Get all reservations (admin only)
router.get('/', requireAuth, requireAdmin, (req, res) => {
  Reservation.getAll((err, reservations) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(reservations);
  });
});

// Get user's reservations
router.get('/my', requireAuth, (req, res) => {
  Reservation.getByUser(req.session.user.id, (err, reservations) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(reservations);
  });
});

// Get a single reservation
router.get('/:id', requireAuth, (req, res) => {
  Reservation.getById(req.params.id, (err, reservation) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!reservation) {
      return res.status(404).json({ error: 'Reservation not found' });
    }
    
    // Students can only view their own reservations
    if (req.session.user.user_type === 'student' && reservation.user_id !== req.session.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    res.json(reservation);
  });
});

// Create a new reservation
router.post('/', requireAuth, (req, res) => {
  const { book_id, priority, notes } = req.body;
  
  if (!book_id) {
    return res.status(400).json({ error: 'Book ID is required' });
  }
  
  const reservationData = {
    book_id,
    user_id: req.session.user.id,
    priority: priority || 1,
    notes
  };
  
  Reservation.create(reservationData, (err, reservation) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.status(201).json(reservation);
  });
});

// Update a reservation (admin only)
router.put('/:id', requireAuth, requireAdmin, (req, res) => {
  Reservation.update(req.params.id, req.body, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Reservation updated successfully' });
  });
});

// Cancel a reservation
router.put('/:id/cancel', requireAuth, (req, res) => {
  // First check if user owns this reservation or is admin
  Reservation.getById(req.params.id, (err, reservation) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    if (!reservation) {
      return res.status(404).json({ error: 'Reservation not found' });
    }
    
    // Students can only cancel their own reservations
    if (req.session.user.user_type === 'student' && reservation.user_id !== req.session.user.id) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    Reservation.cancel(req.params.id, (err) => {
      if (err) {
        return res.status(500).json({ error: err.message });
      }
      res.json({ message: 'Reservation cancelled successfully' });
    });
  });
});

// Fulfill a reservation (admin only)
router.put('/:id/fulfill', requireAuth, requireAdmin, (req, res) => {
  Reservation.fulfill(req.params.id, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Reservation fulfilled successfully' });
  });
});

// Delete a reservation (admin only)
router.delete('/:id', requireAuth, requireAdmin, (req, res) => {
  Reservation.delete(req.params.id, (err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Reservation deleted successfully' });
  });
});

// Get reservation statistics (admin only)
router.get('/stats/summary', requireAuth, requireAdmin, (req, res) => {
  Reservation.getStats((err, stats) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(stats);
  });
});

// Cleanup expired reservations (admin only)
router.post('/cleanup/expired', requireAuth, requireAdmin, (req, res) => {
  Reservation.cleanupExpired((err) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json({ message: 'Expired reservations cleaned up successfully' });
  });
});

module.exports = router;
