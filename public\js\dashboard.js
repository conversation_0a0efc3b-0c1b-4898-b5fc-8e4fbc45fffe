// Dashboard JavaScript
let currentUser = null;
let currentSection = 'dashboard';

document.addEventListener('DOMContentLoaded', async () => {
  // Check authentication first
  await checkAuth();

  // Initialize navigation
  initializeNavigation();

  // Initialize search functionality
  initializeSearch();

  // Initialize filter tabs
  initializeFilterTabs();

  // Load initial data after authentication is confirmed
  loadDashboardData();
});

async function checkAuth() {
  try {
    const response = await fetch('/api/auth/status');
    const data = await response.json();
    
    if (!data.authenticated) {
      window.location.href = '/';
      return;
    }
    
    currentUser = data.user;
    updateUserInterface();
    
  } catch (error) {
    console.error('Auth check failed:', error);
    window.location.href = '/';
  }
}

function updateUserInterface() {
  // Update user info in navbar
  document.getElementById('userName').textContent = currentUser.full_name;
  document.getElementById('userRole').textContent = currentUser.user_type.replace('_', ' ');
  
  // Show/hide admin-only elements
  if (currentUser.user_type === 'library_admin') {
    document.body.classList.add('admin');
  } else {
    document.body.classList.remove('admin');
  }
}

function initializeNavigation() {
  const navLinks = document.querySelectorAll('.nav-link');
  
  navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const section = link.dataset.section;
      
      if (section) {
        switchSection(section);
        
        // Update active nav link
        navLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');
      }
    });
  });
}

function switchSection(section) {
  // Hide all sections
  document.querySelectorAll('.content-section').forEach(s => {
    s.classList.remove('active');
  });

  // Show selected section
  const targetSection = document.getElementById(section + 'Section');
  if (targetSection) {
    targetSection.classList.add('active');
    currentSection = section;

    // Close mobile menu if open
    const navMenu = document.getElementById('navMenu');
    const overlay = document.querySelector('.mobile-nav-overlay');
    if (navMenu) navMenu.classList.remove('show');
    if (overlay) overlay.classList.remove('show');

    // Load section-specific data
    loadSectionData(section);
  }
}

function loadSectionData(section) {
  // Safety check for currentUser
  if (!currentUser) {
    console.warn('currentUser not available yet, skipping section data load');
    return;
  }

  switch (section) {
    case 'dashboard':
      loadDashboardData();
      break;
    case 'books':
      loadBooks();
      break;
    case 'catalog':
      loadCatalog();
      break;
    case 'users':
      if (currentUser.user_type === 'library_admin') {
        loadUsers();
      }
      break;
    case 'issues':
      if (currentUser.user_type === 'library_admin') {
        loadIssues();
      }
      break;
    case 'reservations':
      loadReservations();
      break;
    case 'fines':
      if (currentUser.user_type === 'library_admin') {
        loadFines();
      }
      break;
    case 'acquisitions':
      if (currentUser.user_type === 'library_admin') {
        loadAcquisitions();
      }
      break;
    case 'reports':
      if (currentUser.user_type === 'library_admin') {
        loadReports();
      }
      break;
  }
}

async function loadDashboardData() {
  try {
    showLoading(true);
    
    // Load statistics
    await loadStatistics();
    
    // Load recent activities
    await loadRecentActivities();
    
  } catch (error) {
    console.error('Error loading dashboard data:', error);
  } finally {
    showLoading(false);
  }
}

async function loadStatistics() {
  try {
    // Safety check for currentUser
    if (!currentUser) {
      console.warn('currentUser not available yet, skipping statistics load');
      return;
    }

    const [booksResponse, issuesResponse] = await Promise.all([
      fetch('/api/books'),
      currentUser.user_type === 'library_admin' ? fetch('/api/issues/stats/summary') : Promise.resolve({ json: () => ({}) })
    ]);

    const books = await booksResponse.json();
    const issueStats = currentUser.user_type === 'library_admin' ? await issuesResponse.json() : {};

    // Update book statistics
    document.getElementById('totalBooks').textContent = books.length || 0;

    if (currentUser.user_type === 'library_admin') {
      // Load user count
      const usersResponse = await fetch('/api/users');
      const users = await usersResponse.json();

      document.getElementById('totalUsers').textContent = users.length || 0;
      document.getElementById('issuedBooks').textContent = issueStats.totalIssued || 0;
      document.getElementById('overdueBooks').textContent = issueStats.totalOverdue || 0;
    }

  } catch (error) {
    console.error('Error loading statistics:', error);
  }
}

async function loadRecentActivities() {
  const activitiesContainer = document.getElementById('recentActivities');

  try {
    // Safety check for currentUser
    if (!currentUser) {
      console.warn('currentUser not available yet, skipping activities load');
      activitiesContainer.innerHTML = '<p class="text-center" style="color: #7f8c8d; padding: 20px;">Loading activities...</p>';
      return;
    }

    let activities = [];

    if (currentUser.user_type === 'library_admin') {
      // Load recent issues for admin
      const response = await fetch('/api/issues');
      const issues = await response.json();

      activities = issues.slice(0, 5).map(issue => ({
        icon: 'fas fa-hand-holding',
        iconColor: '#3498db',
        title: `Book issued: ${issue.book_title}`,
        description: `To ${issue.user_name} on ${new Date(issue.issue_date).toLocaleDateString()}`,
        time: new Date(issue.issue_date).toLocaleTimeString()
      }));
    } else {
      // Load user's own issues
      const response = await fetch('/api/issues/my-issues');
      const issues = await response.json();

      activities = issues.slice(0, 5).map(issue => ({
        icon: 'fas fa-book',
        iconColor: issue.status === 'overdue' ? '#e74c3c' : '#27ae60',
        title: `${issue.book_title}`,
        description: `Due: ${new Date(issue.due_date).toLocaleDateString()}`,
        time: issue.status
      }));
    }

    if (activities.length === 0) {
      activitiesContainer.innerHTML = '<p class="text-center" style="color: #7f8c8d; padding: 20px;">No recent activities</p>';
      return;
    }

    activitiesContainer.innerHTML = activities.map(activity => `
      <div class="activity-item">
        <div class="activity-icon" style="background: ${activity.iconColor}">
          <i class="${activity.icon}"></i>
        </div>
        <div class="activity-info">
          <h4>${activity.title}</h4>
          <p>${activity.description}</p>
        </div>
        <span style="font-size: 0.8rem; color: #7f8c8d;">${activity.time}</span>
      </div>
    `).join('');

  } catch (error) {
    console.error('Error loading recent activities:', error);
    activitiesContainer.innerHTML = '<p class="text-center" style="color: #e74c3c; padding: 20px;">Error loading activities</p>';
  }
}

async function loadBooks() {
  try {
    showLoading(true);
    const response = await fetch('/api/books');
    const books = await response.json();
    
    renderBooksTable(books);
    
  } catch (error) {
    console.error('Error loading books:', error);
  } finally {
    showLoading(false);
  }
}

function renderBooksTable(books) {
  const tbody = document.getElementById('booksTableBody');

  if (books.length === 0) {
    tbody.innerHTML = '<tr><td colspan="8" class="text-center" style="padding: 40px; color: #7f8c8d;">No books found</td></tr>';
    return;
  }

  // Safety check for currentUser
  const isAdmin = currentUser && currentUser.user_type === 'library_admin';

  tbody.innerHTML = books.map(book => `
    <tr>
      <td>
        ${book.cover_image ?
          `<img src="${book.cover_image}" alt="${book.title}" class="book-cover">` :
          '<div class="book-cover" style="background: #e1e8ed; display: flex; align-items: center; justify-content: center; color: #7f8c8d;"><i class="fas fa-book"></i></div>'
        }
      </td>
      <td><strong>${book.title}</strong></td>
      <td>${book.author}</td>
      <td>${book.isbn || 'N/A'}</td>
      <td>${book.category || 'Uncategorized'}</td>
      <td>${book.available_copies || 0}/${book.total_copies || 1}</td>
      <td>
        <span class="status-badge status-${book.status}">
          ${book.status}
        </span>
      </td>
      ${isAdmin ? `
        <td>
          <button class="btn btn-warning btn-sm" onclick="editBook(${book.id})">
            <i class="fas fa-edit"></i>
          </button>
          <button class="btn btn-danger btn-sm" onclick="deleteBook(${book.id})">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      ` : '<td></td>'}
    </tr>
  `).join('');
}

async function loadUsers() {
  if (currentUser.user_type !== 'library_admin') return;
  
  try {
    showLoading(true);
    const response = await fetch('/api/users');
    const users = await response.json();
    
    renderUsersTable(users);
    
  } catch (error) {
    console.error('Error loading users:', error);
  } finally {
    showLoading(false);
  }
}

function renderUsersTable(users) {
  const tbody = document.getElementById('usersTableBody');
  
  if (users.length === 0) {
    tbody.innerHTML = '<tr><td colspan="7" class="text-center" style="padding: 40px; color: #7f8c8d;">No users found</td></tr>';
    return;
  }
  
  tbody.innerHTML = users.map(user => `
    <tr>
      <td><strong>${user.full_name}</strong></td>
      <td>${user.email}</td>
      <td>${user.phone || 'N/A'}</td>
      <td>
        <span class="status-badge ${user.user_type === 'library_admin' ? 'status-overdue' : 'status-available'}">
          ${user.user_type.replace('_', ' ')}
        </span>
      </td>
      <td>${user.student_id || 'N/A'}</td>
      <td>
        <span class="status-badge ${user.is_active ? 'status-available' : 'status-issued'}">
          ${user.is_active ? 'Active' : 'Inactive'}
        </span>
      </td>
      <td>
        <button class="btn btn-warning btn-sm" onclick="editUser(${user.id})">
          <i class="fas fa-edit"></i>
        </button>
        ${user.id !== currentUser.id ? `
          <button class="btn btn-danger btn-sm" onclick="deleteUser(${user.id})">
            <i class="fas fa-trash"></i>
          </button>
        ` : ''}
      </td>
    </tr>
  `).join('');
}

function initializeSearch() {
  const bookSearch = document.getElementById('bookSearch');
  const userSearch = document.getElementById('userSearch');
  
  if (bookSearch) {
    bookSearch.addEventListener('input', debounce(searchBooks, 300));
  }
  
  if (userSearch) {
    userSearch.addEventListener('input', debounce(searchUsers, 300));
  }
}

function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

async function searchBooks() {
  const searchTerm = document.getElementById('bookSearch').value;
  
  try {
    const response = await fetch('/api/books');
    const allBooks = await response.json();
    
    const filteredBooks = allBooks.filter(book => 
      book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (book.isbn && book.isbn.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (book.category && book.category.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    renderBooksTable(filteredBooks);
    
  } catch (error) {
    console.error('Error searching books:', error);
  }
}

async function searchUsers() {
  const searchTerm = document.getElementById('userSearch').value;
  
  try {
    const response = await fetch('/api/users');
    const allUsers = await response.json();
    
    const filteredUsers = allUsers.filter(user => 
      user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.student_id && user.student_id.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    renderUsersTable(filteredUsers);
    
  } catch (error) {
    console.error('Error searching users:', error);
  }
}

function initializeFilterTabs() {
  const filterTabs = document.querySelectorAll('.filter-tab');
  
  filterTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // Update active tab
      filterTabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');
      
      // Filter issues based on selected tab
      const filter = tab.dataset.filter;
      filterIssues(filter);
    });
  });
}

async function filterIssues(filter) {
  try {
    let endpoint = '/api/issues';
    
    switch (filter) {
      case 'active':
        endpoint = '/api/issues/active';
        break;
      case 'overdue':
        endpoint = '/api/issues/overdue';
        break;
      case 'all':
      default:
        endpoint = '/api/issues';
        break;
    }
    
    const response = await fetch(endpoint);
    const issues = await response.json();
    
    let filteredIssues = issues;
    if (filter === 'returned') {
      filteredIssues = issues.filter(issue => issue.status === 'returned');
    }
    
    renderIssuesTable(filteredIssues);
    
  } catch (error) {
    console.error('Error filtering issues:', error);
  }
}

function toggleUserMenu() {
  const userMenu = document.getElementById('userMenu');
  userMenu.classList.toggle('show');
}

function toggleMobileMenu() {
  const navMenu = document.getElementById('navMenu');

  if (navMenu.classList.contains('show')) {
    navMenu.classList.remove('show');
    removeMobileOverlay();
  } else {
    navMenu.classList.add('show');
    createMobileOverlay();
  }
}

function createMobileOverlay() {
  let overlay = document.querySelector('.mobile-nav-overlay');
  if (!overlay) {
    overlay = document.createElement('div');
    overlay.className = 'mobile-nav-overlay';
    overlay.addEventListener('click', toggleMobileMenu);
    document.body.appendChild(overlay);
  }
  overlay.classList.add('show');
}

function removeMobileOverlay() {
  const overlay = document.querySelector('.mobile-nav-overlay');
  if (overlay) {
    overlay.classList.remove('show');
  }
}

async function logout() {
  try {
    await fetch('/api/auth/logout', { method: 'POST' });
    sessionStorage.clear();
    localStorage.removeItem('rememberedEmail');
    window.location.href = '/';
  } catch (error) {
    console.error('Logout error:', error);
    window.location.href = '/';
  }
}

function showLoading(show) {
  const overlay = document.getElementById('loadingOverlay');
  overlay.style.display = show ? 'flex' : 'none';
}

// Close user menu when clicking outside
document.addEventListener('click', (e) => {
  const userMenu = document.getElementById('userMenu');
  const userInfo = document.querySelector('.user-info');
  
  if (!userInfo.contains(e.target)) {
    userMenu.classList.remove('show');
  }
});

// Global functions for modal interactions
window.showAddBookModal = () => showModal('addBook');
window.showAddUserModal = () => showModal('addUser');
window.showIssueBookModal = () => showModal('issueBook');
window.showProfile = () => showModal('profile');
window.showChangePassword = () => showModal('changePassword');

window.editBook = (id) => showModal('editBook', id);
window.editUser = (id) => showModal('editUser', id);
window.deleteBook = (id) => confirmDelete('book', id);
window.deleteUser = (id) => confirmDelete('user', id);

// Additional functions for dashboard
async function loadIssues() {
  if (currentUser.user_type !== 'library_admin') return;

  try {
    showLoading(true);
    const response = await fetch('/api/issues');
    const issues = await response.json();

    renderIssuesTable(issues);

  } catch (error) {
    console.error('Error loading issues:', error);
  } finally {
    showLoading(false);
  }
}

function renderIssuesTable(issues) {
  const tbody = document.getElementById('issuesTableBody');

  if (issues.length === 0) {
    tbody.innerHTML = '<tr><td colspan="8" class="text-center" style="padding: 40px; color: #7f8c8d;">No issues found</td></tr>';
    return;
  }

  tbody.innerHTML = issues.map(issue => `
    <tr>
      <td><strong>${issue.book_title}</strong><br><small>${issue.book_author}</small></td>
      <td><strong>${issue.user_name}</strong><br><small>${issue.student_id || 'N/A'}</small></td>
      <td>${Utils.formatDate(issue.issue_date)}</td>
      <td>${Utils.formatDate(issue.due_date)}</td>
      <td>${issue.return_date ? Utils.formatDate(issue.return_date) : 'Not returned'}</td>
      <td>
        <span class="status-badge status-${issue.status}">
          ${issue.status}
        </span>
      </td>
      <td>$${issue.fine_amount || '0.00'}</td>
      <td>
        ${issue.status === 'issued' ? `
          <button class="btn btn-success btn-sm" onclick="returnBook(${issue.id})">
            <i class="fas fa-undo"></i> Return
          </button>
        ` : ''}
        <button class="btn btn-danger btn-sm" onclick="deleteIssue(${issue.id})">
          <i class="fas fa-trash"></i>
        </button>
      </td>
    </tr>
  `).join('');
}

async function loadReports() {
  if (currentUser.user_type !== 'library_admin') return;

  try {
    showLoading(true);

    // Load book statistics
    const booksResponse = await fetch('/api/books');
    const books = await booksResponse.json();

    // Load issue statistics
    const issueStatsResponse = await fetch('/api/issues/stats/summary');
    const issueStats = await issueStatsResponse.json();

    // Load overdue issues
    const overdueResponse = await fetch('/api/issues/overdue');
    const overdueIssues = await overdueResponse.json();

    // Render reports
    renderBookStats(books);
    renderIssueStats(issueStats);
    renderOverdueReport(overdueIssues);
    renderPopularBooks(books);

  } catch (error) {
    console.error('Error loading reports:', error);
  } finally {
    showLoading(false);
  }
}

function renderBookStats(books) {
  const container = document.getElementById('bookStats');
  const totalBooks = books.length;
  const totalCopies = books.reduce((sum, book) => sum + (book.total_copies || 1), 0);
  const availableCopies = books.reduce((sum, book) => sum + (book.available_copies || 0), 0);
  const categories = [...new Set(books.map(book => book.category).filter(Boolean))];

  container.innerHTML = `
    <div class="stat-item">
      <strong>Total Books:</strong> ${totalBooks}
    </div>
    <div class="stat-item">
      <strong>Total Copies:</strong> ${totalCopies}
    </div>
    <div class="stat-item">
      <strong>Available Copies:</strong> ${availableCopies}
    </div>
    <div class="stat-item">
      <strong>Categories:</strong> ${categories.length}
    </div>
  `;
}

function renderIssueStats(stats) {
  const container = document.getElementById('issueStats');

  container.innerHTML = `
    <div class="stat-item">
      <strong>Total Issued:</strong> ${stats.totalIssued || 0}
    </div>
    <div class="stat-item">
      <strong>Total Returned:</strong> ${stats.totalReturned || 0}
    </div>
    <div class="stat-item">
      <strong>Overdue Books:</strong> ${stats.totalOverdue || 0}
    </div>
    <div class="stat-item">
      <strong>Total Fines:</strong> $${stats.totalFines || '0.00'}
    </div>
  `;
}

function renderOverdueReport(overdueIssues) {
  const container = document.getElementById('overdueReport');

  if (overdueIssues.length === 0) {
    container.innerHTML = '<p style="color: #27ae60;">No overdue books!</p>';
    return;
  }

  container.innerHTML = `
    <div class="overdue-list">
      ${overdueIssues.slice(0, 5).map(issue => `
        <div class="overdue-item">
          <strong>${issue.book_title}</strong><br>
          <small>${issue.user_name} - ${Utils.calculateDaysOverdue(issue.due_date)} days overdue</small>
        </div>
      `).join('')}
      ${overdueIssues.length > 5 ? `<p><small>... and ${overdueIssues.length - 5} more</small></p>` : ''}
    </div>
  `;
}

function renderPopularBooks(books) {
  const container = document.getElementById('popularBooks');

  // Calculate popularity based on issued copies
  const popularBooks = books
    .map(book => ({
      ...book,
      issuedCopies: (book.total_copies || 1) - (book.available_copies || 0)
    }))
    .filter(book => book.issuedCopies > 0)
    .sort((a, b) => b.issuedCopies - a.issuedCopies)
    .slice(0, 5);

  if (popularBooks.length === 0) {
    container.innerHTML = '<p style="color: #7f8c8d;">No books currently issued</p>';
    return;
  }

  container.innerHTML = `
    <div class="popular-list">
      ${popularBooks.map((book, index) => `
        <div class="popular-item">
          <span class="rank">${index + 1}.</span>
          <strong>${book.title}</strong><br>
          <small>${book.author} - ${book.issuedCopies} copies issued</small>
        </div>
      `).join('')}
    </div>
  `;
}

async function returnBook(issueId) {
  try {
    await IssuesAPI.returnBook(issueId);
    Utils.showNotification('Book returned successfully', 'success');

    // Refresh current view
    if (currentSection === 'issues') {
      loadIssues();
    }
    loadStatistics();

  } catch (error) {
    console.error('Error returning book:', error);
    Utils.showNotification(error.message || 'Error returning book', 'error');
  }
}

async function deleteIssue(issueId) {
  Utils.showConfirm(
    'Are you sure you want to delete this issue record?',
    async () => {
      try {
        await IssuesAPI.delete(issueId);
        Utils.showNotification('Issue deleted successfully', 'success');

        if (currentSection === 'issues') {
          loadIssues();
        }
        loadStatistics();

      } catch (error) {
        console.error('Error deleting issue:', error);
        Utils.showNotification(error.message || 'Error deleting issue', 'error');
      }
    }
  );
}

function confirmDelete(type, id) {
  const message = `Are you sure you want to delete this ${type}? This action cannot be undone.`;

  Utils.showConfirm(message, async () => {
    try {
      if (type === 'book') {
        await BooksAPI.delete(id);
        Utils.showNotification('Book deleted successfully', 'success');
        if (currentSection === 'books') {
          loadBooks();
        }
      } else if (type === 'user') {
        await UsersAPI.delete(id);
        Utils.showNotification('User deleted successfully', 'success');
        if (currentSection === 'users') {
          loadUsers();
        }
      }

      loadStatistics();

    } catch (error) {
      console.error(`Error deleting ${type}:`, error);
      Utils.showNotification(error.message || `Error deleting ${type}`, 'error');
    }
  });
}

// New section loading functions
async function loadCatalog() {
  try {
    showLoading(true);
    const response = await fetch('/api/books');
    const books = await response.json();

    renderCatalogGrid(books);

  } catch (error) {
    console.error('Error loading catalog:', error);
  } finally {
    showLoading(false);
  }
}

function renderCatalogGrid(books) {
  const catalogGrid = document.getElementById('catalogGrid');

  if (books.length === 0) {
    catalogGrid.innerHTML = '<p class="text-center" style="color: #7f8c8d; padding: 40px;">No books found in catalog</p>';
    return;
  }

  catalogGrid.innerHTML = books.map(book => `
    <div class="catalog-item">
      <div class="catalog-cover">
        ${book.cover_image ?
          `<img src="${book.cover_image}" alt="${book.title}">` :
          '<div class="placeholder-cover"><i class="fas fa-book"></i></div>'
        }
      </div>
      <div class="catalog-info">
        <h3>${book.title}</h3>
        <p class="author">by ${book.author}</p>
        <p class="category">${book.category || 'Uncategorized'}</p>
        <div class="availability">
          <span class="available-count">${book.available_copies || 0} available</span>
          <span class="total-count">of ${book.total_copies || 1}</span>
        </div>
        ${book.available_copies > 0 ?
          '<button class="btn btn-primary btn-sm" onclick="reserveBook(' + book.id + ')">Reserve</button>' :
          '<button class="btn btn-secondary btn-sm" disabled>Not Available</button>'
        }
      </div>
    </div>
  `).join('');
}

async function loadReservations() {
  try {
    showLoading(true);
    const endpoint = currentUser.user_type === 'library_admin' ? '/api/reservations' : '/api/reservations/my';
    const response = await fetch(endpoint);
    const reservations = await response.json();

    renderReservationsTable(reservations);

  } catch (error) {
    console.error('Error loading reservations:', error);
  } finally {
    showLoading(false);
  }
}

function renderReservationsTable(reservations) {
  const tbody = document.getElementById('reservationsTableBody');

  if (reservations.length === 0) {
    tbody.innerHTML = '<tr><td colspan="6" class="text-center" style="padding: 40px; color: #7f8c8d;">No reservations found</td></tr>';
    return;
  }

  tbody.innerHTML = reservations.map(reservation => `
    <tr>
      <td><strong>${reservation.book_title}</strong><br><small>${reservation.book_author}</small></td>
      <td><strong>${reservation.user_name}</strong><br><small>${reservation.student_id || 'N/A'}</small></td>
      <td>${Utils.formatDate(reservation.reserved_date)}</td>
      <td>
        <span class="status-badge status-${reservation.status}">
          ${reservation.status}
        </span>
      </td>
      <td>
        <span class="priority-badge priority-${reservation.priority}">
          Priority ${reservation.priority}
        </span>
      </td>
      <td>
        ${reservation.status === 'active' ? `
          <button class="btn btn-warning btn-sm" onclick="cancelReservation(${reservation.id})">
            <i class="fas fa-times"></i> Cancel
          </button>
          ${currentUser.user_type === 'library_admin' ? `
            <button class="btn btn-success btn-sm" onclick="fulfillReservation(${reservation.id})">
              <i class="fas fa-check"></i> Fulfill
            </button>
          ` : ''}
        ` : ''}
      </td>
    </tr>
  `).join('');
}

async function loadFines() {
  try {
    showLoading(true);
    const endpoint = currentUser.user_type === 'library_admin' ? '/api/fines' : '/api/fines/my';
    const response = await fetch(endpoint);
    const fines = await response.json();

    renderFinesTable(fines);

  } catch (error) {
    console.error('Error loading fines:', error);
  } finally {
    showLoading(false);
  }
}

function renderFinesTable(fines) {
  const tbody = document.getElementById('finesTableBody');

  if (fines.length === 0) {
    tbody.innerHTML = '<tr><td colspan="7" class="text-center" style="padding: 40px; color: #7f8c8d;">No fines found</td></tr>';
    return;
  }

  tbody.innerHTML = fines.map(fine => `
    <tr>
      <td><strong>${fine.user_name}</strong><br><small>${fine.student_id || 'N/A'}</small></td>
      <td><strong>${fine.book_title || 'N/A'}</strong><br><small>${fine.book_author || ''}</small></td>
      <td>$${fine.amount}</td>
      <td>${fine.reason}</td>
      <td>${Utils.formatDate(fine.issued_date)}</td>
      <td>
        <span class="status-badge status-${fine.status}">
          ${fine.status}
        </span>
      </td>
      <td>
        ${fine.status === 'pending' || fine.status === 'partial' ? `
          <button class="btn btn-success btn-sm" onclick="payFine(${fine.id}, ${fine.amount - (fine.paid_amount || 0)})">
            <i class="fas fa-dollar-sign"></i> Pay
          </button>
          ${currentUser.user_type === 'library_admin' ? `
            <button class="btn btn-warning btn-sm" onclick="waiveFine(${fine.id})">
              <i class="fas fa-times"></i> Waive
            </button>
          ` : ''}
        ` : ''}
      </td>
    </tr>
  `).join('');
}

async function loadAcquisitions() {
  try {
    showLoading(true);
    const endpoint = currentUser.user_type === 'library_admin' ? '/api/acquisitions' : '/api/acquisitions/my';
    const response = await fetch(endpoint);
    const acquisitions = await response.json();

    // Load stats for admin
    if (currentUser.user_type === 'library_admin') {
      await loadAcquisitionStats();
    }

    renderAcquisitionsTable(acquisitions);

  } catch (error) {
    console.error('Error loading acquisitions:', error);
  } finally {
    showLoading(false);
  }
}

async function loadAcquisitionStats() {
  try {
    const response = await fetch('/api/acquisitions/stats/summary');
    const stats = await response.json();

    document.getElementById('totalRequests').textContent = stats.total_requests || 0;
    document.getElementById('pendingRequests').textContent = stats.pending_count || 0;
    document.getElementById('estimatedCost').textContent = '$' + (stats.total_estimated_cost || 0).toFixed(2);
    document.getElementById('approvedRequests').textContent = stats.approved_count || 0;

  } catch (error) {
    console.error('Error loading acquisition stats:', error);
  }
}

// Export additional functions
window.returnBook = returnBook;
window.deleteIssue = deleteIssue;
window.confirmDelete = confirmDelete;
window.reserveBook = (id) => showModal('reserveBook', id);
window.showAcquisitionRequestModal = () => showModal('acquisitionRequest');
