// Dashboard JavaScript
let currentUser = null;
let currentSection = 'dashboard';

document.addEventListener('DOMContentLoaded', async () => {
  // Check authentication first
  await checkAuth();

  // Initialize navigation
  initializeNavigation();

  // Initialize search functionality
  initializeSearch();

  // Initialize filter tabs
  initializeFilterTabs();

  // Load initial data after authentication is confirmed
  loadDashboardData();
});

async function checkAuth() {
  try {
    const response = await fetch('/api/auth/status');
    const data = await response.json();
    
    if (!data.authenticated) {
      window.location.href = '/';
      return;
    }
    
    currentUser = data.user;
    updateUserInterface();
    
  } catch (error) {
    console.error('Auth check failed:', error);
    window.location.href = '/';
  }
}

function updateUserInterface() {
  // Update user info in navbar
  document.getElementById('userName').textContent = currentUser.full_name;
  document.getElementById('userRole').textContent = currentUser.user_type.replace('_', ' ');
  
  // Show/hide admin-only elements
  if (currentUser.user_type === 'library_admin') {
    document.body.classList.add('admin');
  } else {
    document.body.classList.remove('admin');
  }
}

function initializeNavigation() {
  const navLinks = document.querySelectorAll('.nav-link');
  
  navLinks.forEach(link => {
    link.addEventListener('click', (e) => {
      e.preventDefault();
      const section = link.dataset.section;
      
      if (section) {
        switchSection(section);
        
        // Update active nav link
        navLinks.forEach(l => l.classList.remove('active'));
        link.classList.add('active');
      }
    });
  });
}

function switchSection(section) {
  // Hide all sections
  document.querySelectorAll('.content-section').forEach(s => {
    s.classList.remove('active');
  });

  // Show selected section
  const targetSection = document.getElementById(section + 'Section');
  if (targetSection) {
    targetSection.classList.add('active');
    currentSection = section;

    // Close mobile menu if open
    const navMenu = document.getElementById('navMenu');
    const overlay = document.querySelector('.mobile-nav-overlay');
    if (navMenu) navMenu.classList.remove('show');
    if (overlay) overlay.classList.remove('show');

    // Load section-specific data
    loadSectionData(section);
  }
}

function loadSectionData(section) {
  // Safety check for currentUser
  if (!currentUser) {
    console.warn('currentUser not available yet, skipping section data load');
    return;
  }

  switch (section) {
    case 'dashboard':
      loadDashboardData();
      break;
    case 'books':
      loadBooks();
      break;
    case 'catalog':
      loadCatalog();
      break;
    case 'users':
      if (currentUser.user_type === 'library_admin') {
        loadUsers();
      }
      break;
    case 'issues':
      if (currentUser.user_type === 'library_admin') {
        loadIssues();
      }
      break;
    case 'reservations':
      loadReservations();
      break;
    case 'fines':
      if (currentUser.user_type === 'library_admin') {
        loadFines();
      }
      break;
    case 'acquisitions':
      if (currentUser.user_type === 'library_admin') {
        loadAcquisitions();
      }
      break;
    case 'reports':
      if (currentUser.user_type === 'library_admin') {
        loadReports();
      }
      break;
  }
}

async function loadDashboardData() {
  try {
    showLoading(true);
    
    // Load statistics
    await loadStatistics();
    
    // Load recent activities
    await loadRecentActivities();
    
  } catch (error) {
    console.error('Error loading dashboard data:', error);
  } finally {
    showLoading(false);
  }
}

async function loadStatistics() {
  try {
    // Safety check for currentUser
    if (!currentUser) {
      console.warn('currentUser not available yet, skipping statistics load');
      return;
    }

    const [booksResponse, issuesResponse] = await Promise.all([
      fetch('/api/books'),
      currentUser.user_type === 'library_admin' ? fetch('/api/issues/stats/summary') : Promise.resolve({ json: () => ({}) })
    ]);

    const books = await booksResponse.json();
    const issueStats = currentUser.user_type === 'library_admin' ? await issuesResponse.json() : {};

    // Update book statistics
    document.getElementById('totalBooks').textContent = books.length || 0;

    if (currentUser.user_type === 'library_admin') {
      // Load user count
      const usersResponse = await fetch('/api/users');
      const users = await usersResponse.json();

      document.getElementById('totalUsers').textContent = users.length || 0;
      document.getElementById('issuedBooks').textContent = issueStats.totalIssued || 0;
      document.getElementById('overdueBooks').textContent = issueStats.totalOverdue || 0;
    }

  } catch (error) {
    console.error('Error loading statistics:', error);
  }
}

async function loadRecentActivities() {
  const activitiesContainer = document.getElementById('recentActivities');

  try {
    // Safety check for currentUser
    if (!currentUser) {
      console.warn('currentUser not available yet, skipping activities load');
      activitiesContainer.innerHTML = '<p class="text-center" style="color: #7f8c8d; padding: 20px;">Loading activities...</p>';
      return;
    }

    let activities = [];

    if (currentUser.user_type === 'library_admin') {
      // Load recent issues for admin
      const response = await fetch('/api/issues');
      const issues = await response.json();

      activities = issues.slice(0, 5).map(issue => ({
        icon: 'fas fa-hand-holding',
        iconColor: '#3498db',
        title: `Book issued: ${issue.book_title}`,
        description: `To ${issue.user_name} on ${new Date(issue.issue_date).toLocaleDateString()}`,
        time: new Date(issue.issue_date).toLocaleTimeString()
      }));
    } else {
      // Load user's own issues
      const response = await fetch('/api/issues/my-issues');
      const issues = await response.json();

      activities = issues.slice(0, 5).map(issue => ({
        icon: 'fas fa-book',
        iconColor: issue.status === 'overdue' ? '#e74c3c' : '#27ae60',
        title: `${issue.book_title}`,
        description: `Due: ${new Date(issue.due_date).toLocaleDateString()}`,
        time: issue.status
      }));
    }

    if (activities.length === 0) {
      activitiesContainer.innerHTML = '<p class="text-center" style="color: #7f8c8d; padding: 20px;">No recent activities</p>';
      return;
    }

    activitiesContainer.innerHTML = activities.map(activity => `
      <div class="activity-item">
        <div class="activity-icon" style="background: ${activity.iconColor}">
          <i class="${activity.icon}"></i>
        </div>
        <div class="activity-info">
          <h4>${activity.title}</h4>
          <p>${activity.description}</p>
        </div>
        <span style="font-size: 0.8rem; color: #7f8c8d;">${activity.time}</span>
      </div>
    `).join('');

  } catch (error) {
    console.error('Error loading recent activities:', error);
    activitiesContainer.innerHTML = '<p class="text-center" style="color: #e74c3c; padding: 20px;">Error loading activities</p>';
  }
}

async function loadBooks() {
  try {
    showLoading(true);
    const response = await fetch('/api/books');
    const books = await response.json();
    
    renderBooksTable(books);
    
  } catch (error) {
    console.error('Error loading books:', error);
  } finally {
    showLoading(false);
  }
}

function renderBooksTable(books) {
  const tbody = document.getElementById('booksTableBody');

  if (books.length === 0) {
    tbody.innerHTML = '<tr><td colspan="8" class="text-center" style="padding: 40px; color: #7f8c8d;">No books found</td></tr>';
    return;
  }

  // Safety check for currentUser
  const isAdmin = currentUser && currentUser.user_type === 'library_admin';

  tbody.innerHTML = books.map(book => `
    <tr>
      <td>
        ${book.cover_image ?
          `<img src="${book.cover_image}" alt="${book.title}" class="book-cover">` :
          '<div class="book-cover" style="background: #e1e8ed; display: flex; align-items: center; justify-content: center; color: #7f8c8d;"><i class="fas fa-book"></i></div>'
        }
      </td>
      <td><strong>${book.title}</strong></td>
      <td>${book.author}</td>
      <td>${book.isbn || 'N/A'}</td>
      <td>${book.category || 'Uncategorized'}</td>
      <td>${book.available_copies || 0}/${book.total_copies || 1}</td>
      <td>
        <span class="status-badge status-${book.status}">
          ${book.status}
        </span>
      </td>
      ${isAdmin ? `
        <td>
          <button class="btn btn-warning btn-sm" onclick="editBook(${book.id})">
            <i class="fas fa-edit"></i>
          </button>
          <button class="btn btn-danger btn-sm" onclick="deleteBook(${book.id})">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      ` : '<td></td>'}
    </tr>
  `).join('');
}

async function loadUsers() {
  if (currentUser.user_type !== 'library_admin') return;
  
  try {
    showLoading(true);
    const response = await fetch('/api/users');
    const users = await response.json();
    
    renderUsersTable(users);
    
  } catch (error) {
    console.error('Error loading users:', error);
  } finally {
    showLoading(false);
  }
}

function renderUsersTable(users) {
  const tbody = document.getElementById('usersTableBody');

  if (users.length === 0) {
    tbody.innerHTML = '<tr><td colspan="7" class="text-center" style="padding: 40px; color: #7f8c8d;">No users found</td></tr>';
    return;
  }

  // Safety check for currentUser
  const isAdmin = currentUser && currentUser.user_type === 'library_admin';

  tbody.innerHTML = users.map(user => {
    // Determine user status
    let statusBadge = '';
    let statusClass = '';

    if (!user.is_active) {
      statusBadge = 'Soft Deleted';
      statusClass = 'status-deleted';
    } else if (user.is_blocked) {
      statusBadge = 'Blocked';
      statusClass = 'status-blocked';
    } else {
      statusBadge = 'Active';
      statusClass = 'status-active';
    }

    // Determine available actions
    const isCurrentUser = currentUser && currentUser.id === user.id;
    let actionButtons = '';

    if (isAdmin && !isCurrentUser) {
      actionButtons = `<button class="btn btn-warning btn-sm" onclick="editUser(${user.id})" title="Edit User"><i class="fas fa-edit"></i><span class="d-none d-lg-inline"> Edit</span></button>${!user.is_active ? `<button class="btn btn-success btn-sm" onclick="restoreUser(${user.id})" title="Restore User"><i class="fas fa-undo"></i><span class="d-none d-xl-inline"> Restore</span></button>` : user.is_blocked ? `<button class="btn btn-info btn-sm" onclick="unblockUser(${user.id})" title="Unblock User"><i class="fas fa-unlock"></i><span class="d-none d-xl-inline"> Unblock</span></button><button class="btn btn-secondary btn-sm" onclick="softDeleteUser(${user.id})" title="Soft Delete"><i class="fas fa-archive"></i><span class="d-none d-xl-inline"> Archive</span></button>` : `<button class="btn btn-warning btn-sm" onclick="blockUser(${user.id})" title="Block User"><i class="fas fa-lock"></i><span class="d-none d-xl-inline"> Block</span></button><button class="btn btn-secondary btn-sm" onclick="softDeleteUser(${user.id})" title="Soft Delete"><i class="fas fa-archive"></i><span class="d-none d-xl-inline"> Archive</span></button>`}<button class="btn btn-danger btn-sm" onclick="deleteUser(${user.id})" title="Permanent Delete"><i class="fas fa-trash"></i><span class="d-none d-xl-inline"> Delete</span></button>`;
    } else if (isAdmin && isCurrentUser) {
      actionButtons = `<button class="btn btn-warning btn-sm" onclick="editUser(${user.id})" title="Edit Profile"><i class="fas fa-edit"></i><span class="d-none d-lg-inline"> Edit</span></button><small class="text-muted">Own account</small>`;
    }

    return `
      <tr class="${!user.is_active ? 'user-deleted' : user.is_blocked ? 'user-blocked' : ''}">
        <td><strong>${user.full_name}</strong></td>
        <td>${user.email}</td>
        <td>${user.phone || 'N/A'}</td>
        <td>
          <span class="status-badge ${user.user_type === 'library_admin' ? 'status-overdue' : 'status-available'}">
            ${user.user_type.replace('_', ' ')}
          </span>
        </td>
        <td>
          <span class="status-badge ${statusClass}">
            ${statusBadge}
          </span>
        </td>
        <td>${user.student_id || 'N/A'}</td>
        <td>
          <div class="action-buttons">
            ${actionButtons}
          </div>
        </td>
      </tr>
    `;
  }).join('');
}

function initializeSearch() {
  const bookSearch = document.getElementById('bookSearch');
  const userSearch = document.getElementById('userSearch');
  
  if (bookSearch) {
    bookSearch.addEventListener('input', debounce(searchBooks, 300));
  }
  
  if (userSearch) {
    userSearch.addEventListener('input', debounce(searchUsers, 300));
  }
}

function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

async function searchBooks() {
  const searchTerm = document.getElementById('bookSearch').value;
  
  try {
    const response = await fetch('/api/books');
    const allBooks = await response.json();
    
    const filteredBooks = allBooks.filter(book => 
      book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (book.isbn && book.isbn.toLowerCase().includes(searchTerm.toLowerCase())) ||
      (book.category && book.category.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    renderBooksTable(filteredBooks);
    
  } catch (error) {
    console.error('Error searching books:', error);
  }
}

async function searchUsers() {
  const searchTerm = document.getElementById('userSearch').value;
  
  try {
    const response = await fetch('/api/users');
    const allUsers = await response.json();
    
    const filteredUsers = allUsers.filter(user => 
      user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (user.student_id && user.student_id.toLowerCase().includes(searchTerm.toLowerCase()))
    );
    
    renderUsersTable(filteredUsers);
    
  } catch (error) {
    console.error('Error searching users:', error);
  }
}

function initializeFilterTabs() {
  const filterTabs = document.querySelectorAll('.filter-tab');
  
  filterTabs.forEach(tab => {
    tab.addEventListener('click', () => {
      // Update active tab
      filterTabs.forEach(t => t.classList.remove('active'));
      tab.classList.add('active');
      
      // Filter issues based on selected tab
      const filter = tab.dataset.filter;
      filterIssues(filter);
    });
  });
}

async function filterIssues(filter) {
  try {
    let endpoint = '/api/issues';
    
    switch (filter) {
      case 'active':
        endpoint = '/api/issues/active';
        break;
      case 'overdue':
        endpoint = '/api/issues/overdue';
        break;
      case 'all':
      default:
        endpoint = '/api/issues';
        break;
    }
    
    const response = await fetch(endpoint);
    const issues = await response.json();
    
    let filteredIssues = issues;
    if (filter === 'returned') {
      filteredIssues = issues.filter(issue => issue.status === 'returned');
    }
    
    renderIssuesTable(filteredIssues);
    
  } catch (error) {
    console.error('Error filtering issues:', error);
  }
}

function toggleUserMenu() {
  const userMenu = document.getElementById('userMenu');
  userMenu.classList.toggle('show');
}

function toggleMobileMenu() {
  const navMenu = document.getElementById('navMenu');

  if (navMenu.classList.contains('show')) {
    navMenu.classList.remove('show');
    removeMobileOverlay();
  } else {
    navMenu.classList.add('show');
    createMobileOverlay();
  }
}

function createMobileOverlay() {
  let overlay = document.querySelector('.mobile-nav-overlay');
  if (!overlay) {
    overlay = document.createElement('div');
    overlay.className = 'mobile-nav-overlay';
    overlay.addEventListener('click', toggleMobileMenu);
    document.body.appendChild(overlay);
  }
  overlay.classList.add('show');
}

function removeMobileOverlay() {
  const overlay = document.querySelector('.mobile-nav-overlay');
  if (overlay) {
    overlay.classList.remove('show');
  }
}

async function logout() {
  try {
    await fetch('/api/auth/logout', { method: 'POST' });
    sessionStorage.clear();
    localStorage.removeItem('rememberedEmail');
    window.location.href = '/';
  } catch (error) {
    console.error('Logout error:', error);
    window.location.href = '/';
  }
}

function showLoading(show) {
  const overlay = document.getElementById('loadingOverlay');
  overlay.style.display = show ? 'flex' : 'none';
}

// Close user menu when clicking outside
document.addEventListener('click', (e) => {
  const userMenu = document.getElementById('userMenu');
  const userInfo = document.querySelector('.user-info');
  
  if (!userInfo.contains(e.target)) {
    userMenu.classList.remove('show');
  }
});

// Global functions for modal interactions
window.showAddBookModal = () => showModal('addBook');
window.showAddUserModal = () => showModal('addUser');
window.showIssueBookModal = () => showModal('issueBook');
window.showProfile = () => showModal('profile');
window.showChangePassword = () => showModal('changePassword');

window.editBook = (id) => showModal('editBook', id);
window.editUser = (id) => showModal('editUser', id);
window.deleteBook = (id) => confirmDelete('book', id);
window.deleteUser = (id) => confirmDelete('user', id);
window.softDeleteUser = softDeleteUser;
window.restoreUser = restoreUser;
window.blockUser = blockUser;
window.unblockUser = unblockUser;

// Additional functions for dashboard
async function loadIssues() {
  if (currentUser.user_type !== 'library_admin') return;

  try {
    showLoading(true);
    const response = await fetch('/api/issues');
    const issues = await response.json();

    renderIssuesTable(issues);

  } catch (error) {
    console.error('Error loading issues:', error);
  } finally {
    showLoading(false);
  }
}

function renderIssuesTable(issues) {
  const tbody = document.getElementById('issuesTableBody');

  if (issues.length === 0) {
    tbody.innerHTML = '<tr><td colspan="9" class="text-center" style="padding: 40px; color: #7f8c8d;">No issues found</td></tr>';
    return;
  }

  tbody.innerHTML = issues.map(issue => {
    const issueDate = new Date(issue.issue_date);
    const dueDate = new Date(issue.due_date);
    const now = new Date();

    // Calculate total days issued
    let totalDaysIssued;
    let daysIssuedDisplay;

    if (issue.return_date) {
      // Book has been returned - calculate total days from issue to return
      const returnDate = new Date(issue.return_date);
      totalDaysIssued = Math.floor((returnDate - issueDate) / (1000 * 60 * 60 * 24));
      daysIssuedDisplay = `<span style="color: #27ae60; font-weight: bold;">${totalDaysIssued} day${totalDaysIssued !== 1 ? 's' : ''}</span><br><small style="color: #7f8c8d;">Completed</small>`;
    } else {
      // Book is still issued - calculate running days
      totalDaysIssued = Math.floor((now - issueDate) / (1000 * 60 * 60 * 24));
      daysIssuedDisplay = `<span style="color: #3498db; font-weight: bold;">${totalDaysIssued} day${totalDaysIssued !== 1 ? 's' : ''}</span><br><small style="color: #7f8c8d;">Ongoing</small>`;
    }

    // Calculate overdue status
    const isOverdue = !issue.return_date && now > dueDate;
    const overdueDays = isOverdue ? Math.floor((now - dueDate) / (1000 * 60 * 60 * 24)) : 0;

    return `
      <tr class="${isOverdue ? 'overdue-row' : ''}">
        <td><strong>${issue.book_title}</strong><br><small>${issue.book_author}</small></td>
        <td><strong>${issue.user_name}</strong><br><small>${issue.student_id || 'N/A'}</small></td>
        <td>
          <strong>${Utils.formatDate(issue.issue_date)}</strong><br>
          <small style="color: #7f8c8d;">${Utils.formatDateTime(issue.issue_date).split(' ')[1]}</small>
        </td>
        <td>
          <strong>${Utils.formatDate(issue.due_date)}</strong><br>
          <small style="color: #7f8c8d;">${Utils.formatDateTime(issue.due_date).split(' ')[1]}</small>
          ${isOverdue ? `<br><span style="color: #e74c3c; font-weight: bold;">Overdue by ${overdueDays} day${overdueDays > 1 ? 's' : ''}</span>` : ''}
        </td>
        <td>
          ${issue.return_date ?
            `<strong>${Utils.formatDate(issue.return_date)}</strong><br><small style="color: #7f8c8d;">${Utils.formatDateTime(issue.return_date).split(' ')[1]}</small>` :
            `<span style="color: #e67e22;">Not returned</span><br><small>Active issue</small>`
          }
        </td>
        <td style="text-align: center;">
          ${daysIssuedDisplay}
        </td>
        <td>
          <span class="status-badge status-${issue.status}">
            ${issue.status}
          </span>
        </td>
        <td>$${issue.fine_amount || '0.00'}</td>
        <td>
          ${issue.status === 'issued' ? `
            <button class="btn btn-success btn-sm" onclick="returnBook(${issue.id})">
              <i class="fas fa-undo"></i> Return
            </button>
          ` : ''}
          <button class="btn btn-danger btn-sm" onclick="deleteIssue(${issue.id})">
            <i class="fas fa-trash"></i>
          </button>
        </td>
      </tr>
    `;
  }).join('');
}

async function loadReports() {
  if (currentUser.user_type !== 'library_admin') return;

  try {
    // Load the summary report by default
    loadSummaryReport();
  } catch (error) {
    console.error('Error loading reports:', error);
  }
}

function renderBookStats(books) {
  const container = document.getElementById('bookStats');
  const totalBooks = books.length;
  const totalCopies = books.reduce((sum, book) => sum + (book.total_copies || 1), 0);
  const availableCopies = books.reduce((sum, book) => sum + (book.available_copies || 0), 0);
  const categories = [...new Set(books.map(book => book.category).filter(Boolean))];

  container.innerHTML = `
    <div class="stat-item">
      <strong>Total Books:</strong> ${totalBooks}
    </div>
    <div class="stat-item">
      <strong>Total Copies:</strong> ${totalCopies}
    </div>
    <div class="stat-item">
      <strong>Available Copies:</strong> ${availableCopies}
    </div>
    <div class="stat-item">
      <strong>Categories:</strong> ${categories.length}
    </div>
  `;
}

function renderIssueStats(stats) {
  const container = document.getElementById('issueStats');

  container.innerHTML = `
    <div class="stat-item">
      <strong>Total Issued:</strong> ${stats.totalIssued || 0}
    </div>
    <div class="stat-item">
      <strong>Total Returned:</strong> ${stats.totalReturned || 0}
    </div>
    <div class="stat-item">
      <strong>Overdue Books:</strong> ${stats.totalOverdue || 0}
    </div>
    <div class="stat-item">
      <strong>Total Fines:</strong> $${stats.totalFines || '0.00'}
    </div>
  `;
}

function renderOverdueReport(overdueIssues) {
  const container = document.getElementById('overdueReport');

  if (overdueIssues.length === 0) {
    container.innerHTML = '<p style="color: #27ae60;">No overdue books!</p>';
    return;
  }

  container.innerHTML = `
    <div class="overdue-list">
      ${overdueIssues.slice(0, 5).map(issue => `
        <div class="overdue-item">
          <strong>${issue.book_title}</strong><br>
          <small>${issue.user_name} - ${Utils.calculateDaysOverdue(issue.due_date)} days overdue</small>
        </div>
      `).join('')}
      ${overdueIssues.length > 5 ? `<p><small>... and ${overdueIssues.length - 5} more</small></p>` : ''}
    </div>
  `;
}

function renderPopularBooks(books) {
  const container = document.getElementById('popularBooks');

  // Calculate popularity based on issued copies
  const popularBooks = books
    .map(book => ({
      ...book,
      issuedCopies: (book.total_copies || 1) - (book.available_copies || 0)
    }))
    .filter(book => book.issuedCopies > 0)
    .sort((a, b) => b.issuedCopies - a.issuedCopies)
    .slice(0, 5);

  if (popularBooks.length === 0) {
    container.innerHTML = '<p style="color: #7f8c8d;">No books currently issued</p>';
    return;
  }

  container.innerHTML = `
    <div class="popular-list">
      ${popularBooks.map((book, index) => `
        <div class="popular-item">
          <span class="rank">${index + 1}.</span>
          <strong>${book.title}</strong><br>
          <small>${book.author} - ${book.issuedCopies} copies issued</small>
        </div>
      `).join('')}
    </div>
  `;
}

async function returnBook(issueId) {
  try {
    await IssuesAPI.returnBook(issueId);
    Utils.showNotification('Book returned successfully', 'success');

    // Refresh current view
    if (currentSection === 'issues') {
      loadIssues();
    }
    loadStatistics();

  } catch (error) {
    console.error('Error returning book:', error);
    Utils.showNotification(error.message || 'Error returning book', 'error');
  }
}

async function deleteIssue(issueId) {
  Utils.showConfirm(
    'Are you sure you want to delete this issue record?',
    async () => {
      try {
        await IssuesAPI.delete(issueId);
        Utils.showNotification('Issue deleted successfully', 'success');

        if (currentSection === 'issues') {
          loadIssues();
        }
        loadStatistics();

      } catch (error) {
        console.error('Error deleting issue:', error);
        Utils.showNotification(error.message || 'Error deleting issue', 'error');
      }
    }
  );
}

function confirmDelete(type, id) {
  const message = `Are you sure you want to permanently delete this ${type}? This action cannot be undone.`;

  Utils.showConfirm(message, async () => {
    try {
      if (type === 'book') {
        await BooksAPI.delete(id);
        Utils.showNotification('Book deleted successfully', 'success');
        if (currentSection === 'books') {
          loadBooks();
        }
      } else if (type === 'user') {
        await UsersAPI.delete(id);
        Utils.showNotification('User permanently deleted', 'success');
        if (currentSection === 'users') {
          loadUsers();
        }
      }

      loadStatistics();

    } catch (error) {
      console.error(`Error deleting ${type}:`, error);
      Utils.showNotification(error.message || `Error deleting ${type}`, 'error');
    }
  });
}

// User management functions
async function softDeleteUser(userId) {
  Utils.showConfirm(
    'Are you sure you want to soft delete this user? They will be marked as inactive but their data will be preserved.',
    async () => {
      try {
        await UsersAPI.softDelete(userId);
        Utils.showNotification('User soft deleted successfully', 'success');
        if (currentSection === 'users') {
          loadUsers();
        }
        loadStatistics();
      } catch (error) {
        console.error('Error soft deleting user:', error);
        Utils.showNotification(error.message || 'Error soft deleting user', 'error');
      }
    }
  );
}

async function restoreUser(userId) {
  Utils.showConfirm(
    'Are you sure you want to restore this user? They will be reactivated.',
    async () => {
      try {
        await UsersAPI.restore(userId);
        Utils.showNotification('User restored successfully', 'success');
        if (currentSection === 'users') {
          loadUsers();
        }
        loadStatistics();
      } catch (error) {
        console.error('Error restoring user:', error);
        Utils.showNotification(error.message || 'Error restoring user', 'error');
      }
    }
  );
}

async function blockUser(userId) {
  Utils.showConfirm(
    'Are you sure you want to block this user? They will not be able to issue books or make reservations.',
    async () => {
      try {
        await UsersAPI.blockUser(userId);
        Utils.showNotification('User blocked successfully', 'success');
        if (currentSection === 'users') {
          loadUsers();
        }
        loadStatistics();
      } catch (error) {
        console.error('Error blocking user:', error);
        Utils.showNotification(error.message || 'Error blocking user', 'error');
      }
    }
  );
}

async function unblockUser(userId) {
  Utils.showConfirm(
    'Are you sure you want to unblock this user? They will regain access to library services.',
    async () => {
      try {
        await UsersAPI.unblockUser(userId);
        Utils.showNotification('User unblocked successfully', 'success');
        if (currentSection === 'users') {
          loadUsers();
        }
        loadStatistics();
      } catch (error) {
        console.error('Error unblocking user:', error);
        Utils.showNotification(error.message || 'Error unblocking user', 'error');
      }
    }
  );
}

// New section loading functions
async function loadCatalog() {
  try {
    showLoading(true);
    const response = await fetch('/api/books');
    const books = await response.json();

    // Store all books for filtering
    window.allCatalogBooks = books;

    renderCatalogGrid(books);
    initializeCatalogFilters();

  } catch (error) {
    console.error('Error loading catalog:', error);
  } finally {
    showLoading(false);
  }
}

function initializeCatalogFilters() {
  // Initialize search
  const catalogSearch = document.getElementById('catalogSearch');
  if (catalogSearch) {
    catalogSearch.addEventListener('input', debounce(filterCatalog, 300));
  }

  // Initialize category filter
  const categoryFilter = document.getElementById('categoryFilter');
  if (categoryFilter) {
    // Populate category options
    const categories = [...new Set(window.allCatalogBooks.map(book => book.category).filter(Boolean))];
    categoryFilter.innerHTML = '<option value="">All Categories</option>' +
      categories.map(cat => `<option value="${cat}">${cat}</option>`).join('');

    categoryFilter.addEventListener('change', filterCatalog);
  }

  // Initialize availability filter
  const availabilityFilter = document.getElementById('availabilityFilter');
  if (availabilityFilter) {
    availabilityFilter.addEventListener('change', filterCatalog);
  }
}

function filterCatalog() {
  const searchTerm = document.getElementById('catalogSearch')?.value.toLowerCase() || '';
  const categoryFilter = document.getElementById('categoryFilter')?.value || '';
  const availabilityFilter = document.getElementById('availabilityFilter')?.value || '';

  let filteredBooks = window.allCatalogBooks || [];

  // Apply search filter
  if (searchTerm) {
    filteredBooks = filteredBooks.filter(book =>
      book.title.toLowerCase().includes(searchTerm) ||
      book.author.toLowerCase().includes(searchTerm) ||
      (book.isbn && book.isbn.toLowerCase().includes(searchTerm)) ||
      (book.description && book.description.toLowerCase().includes(searchTerm))
    );
  }

  // Apply category filter
  if (categoryFilter) {
    filteredBooks = filteredBooks.filter(book => book.category === categoryFilter);
  }

  // Apply availability filter
  if (availabilityFilter === 'available') {
    filteredBooks = filteredBooks.filter(book => book.available_copies > 0);
  } else if (availabilityFilter === 'unavailable') {
    filteredBooks = filteredBooks.filter(book => book.available_copies === 0);
  }

  renderCatalogGrid(filteredBooks);
}

function renderCatalogGrid(books) {
  const catalogGrid = document.getElementById('catalogGrid');

  if (books.length === 0) {
    catalogGrid.innerHTML = '<p class="text-center" style="color: #7f8c8d; padding: 40px;">No books found in catalog</p>';
    return;
  }

  catalogGrid.innerHTML = books.map(book => `
    <div class="catalog-item">
      <div class="catalog-cover">
        ${book.cover_image ?
          `<img src="${book.cover_image}" alt="${book.title}">` :
          '<div class="placeholder-cover"><i class="fas fa-book"></i></div>'
        }
      </div>
      <div class="catalog-info">
        <h3>${book.title}</h3>
        <p class="author">by ${book.author}</p>
        <p class="category">${book.category || 'Uncategorized'}</p>
        <div class="availability">
          <span class="available-count">${book.available_copies || 0} available</span>
          <span class="total-count">of ${book.total_copies || 1}</span>
        </div>
        ${book.available_copies > 0 ?
          '<button class="btn btn-primary btn-sm" onclick="reserveBook(' + book.id + ')">Reserve</button>' :
          '<button class="btn btn-secondary btn-sm" disabled>Not Available</button>'
        }
      </div>
    </div>
  `).join('');
}

async function loadReservations() {
  try {
    showLoading(true);
    const endpoint = currentUser.user_type === 'library_admin' ? '/api/reservations' : '/api/reservations/my';
    const response = await fetch(endpoint);
    const reservations = await response.json();

    renderReservationsTable(reservations);

  } catch (error) {
    console.error('Error loading reservations:', error);
  } finally {
    showLoading(false);
  }
}

function renderReservationsTable(reservations) {
  const tbody = document.getElementById('reservationsTableBody');

  if (reservations.length === 0) {
    tbody.innerHTML = '<tr><td colspan="6" class="text-center" style="padding: 40px; color: #7f8c8d;">No reservations found</td></tr>';
    return;
  }

  tbody.innerHTML = reservations.map(reservation => `
    <tr>
      <td><strong>${reservation.book_title}</strong><br><small>${reservation.book_author}</small></td>
      <td><strong>${reservation.user_name}</strong><br><small>${reservation.student_id || 'N/A'}</small></td>
      <td>${Utils.formatDate(reservation.reserved_date)}</td>
      <td>
        <span class="status-badge status-${reservation.status}">
          ${reservation.status}
        </span>
      </td>
      <td>
        <span class="priority-badge priority-${reservation.priority}">
          Priority ${reservation.priority}
        </span>
      </td>
      <td>
        ${reservation.status === 'active' ? `
          <button class="btn btn-warning btn-sm" onclick="cancelReservation(${reservation.id})">
            <i class="fas fa-times"></i> Cancel
          </button>
          ${currentUser.user_type === 'library_admin' ? `
            <button class="btn btn-success btn-sm" onclick="fulfillReservation(${reservation.id})">
              <i class="fas fa-check"></i> Fulfill
            </button>
          ` : ''}
        ` : ''}
      </td>
    </tr>
  `).join('');
}

async function loadFines() {
  try {
    showLoading(true);
    const endpoint = currentUser.user_type === 'library_admin' ? '/api/fines' : '/api/fines/my';
    const response = await fetch(endpoint);
    const fines = await response.json();

    renderFinesTable(fines);

  } catch (error) {
    console.error('Error loading fines:', error);
  } finally {
    showLoading(false);
  }
}

function renderFinesTable(fines) {
  const tbody = document.getElementById('finesTableBody');

  if (fines.length === 0) {
    tbody.innerHTML = '<tr><td colspan="7" class="text-center" style="padding: 40px; color: #7f8c8d;">No fines found</td></tr>';
    return;
  }

  tbody.innerHTML = fines.map(fine => `
    <tr>
      <td><strong>${fine.user_name}</strong><br><small>${fine.student_id || 'N/A'}</small></td>
      <td><strong>${fine.book_title || 'N/A'}</strong><br><small>${fine.book_author || ''}</small></td>
      <td>$${fine.amount}</td>
      <td>${fine.reason}</td>
      <td>${Utils.formatDate(fine.issued_date)}</td>
      <td>
        <span class="status-badge status-${fine.status}">
          ${fine.status}
        </span>
      </td>
      <td>
        ${fine.status === 'pending' || fine.status === 'partial' ? `
          <button class="btn btn-success btn-sm" onclick="payFine(${fine.id}, ${fine.amount - (fine.paid_amount || 0)})">
            <i class="fas fa-dollar-sign"></i> Pay
          </button>
          ${currentUser.user_type === 'library_admin' ? `
            <button class="btn btn-warning btn-sm" onclick="waiveFine(${fine.id})">
              <i class="fas fa-times"></i> Waive
            </button>
          ` : ''}
        ` : ''}
      </td>
    </tr>
  `).join('');
}

async function loadAcquisitions() {
  try {
    showLoading(true);
    const endpoint = currentUser.user_type === 'library_admin' ? '/api/acquisitions' : '/api/acquisitions/my';
    const response = await fetch(endpoint);
    const acquisitions = await response.json();

    // Load stats for admin
    if (currentUser.user_type === 'library_admin') {
      await loadAcquisitionStats();
    }

    renderAcquisitionsTable(acquisitions);

  } catch (error) {
    console.error('Error loading acquisitions:', error);
  } finally {
    showLoading(false);
  }
}

async function loadAcquisitionStats() {
  try {
    const response = await fetch('/api/acquisitions/stats/summary');
    const stats = await response.json();

    document.getElementById('totalRequests').textContent = stats.total_requests || 0;
    document.getElementById('pendingRequests').textContent = stats.pending_count || 0;
    document.getElementById('estimatedCost').textContent = '$' + (stats.total_estimated_cost || 0).toFixed(2);
    document.getElementById('approvedRequests').textContent = stats.approved_count || 0;

  } catch (error) {
    console.error('Error loading acquisition stats:', error);
  }
}

function renderAcquisitionsTable(acquisitions) {
  const tbody = document.getElementById('acquisitionsTableBody');

  if (acquisitions.length === 0) {
    tbody.innerHTML = '<tr><td colspan="9" class="text-center" style="padding: 40px; color: #7f8c8d;">No acquisition requests found</td></tr>';
    return;
  }

  // Safety check for currentUser
  const isAdmin = currentUser && currentUser.user_type === 'library_admin';

  tbody.innerHTML = acquisitions.map(acquisition => `
    <tr>
      <td><strong>${acquisition.title}</strong></td>
      <td>${acquisition.author}</td>
      <td>${acquisition.isbn || 'N/A'}</td>
      <td>$${acquisition.estimated_cost || '0.00'}</td>
      <td>
        <span class="priority-badge priority-${acquisition.priority}">
          ${acquisition.priority}
        </span>
      </td>
      <td>
        <span class="status-badge status-${acquisition.status}">
          ${acquisition.status}
        </span>
      </td>
      <td>${acquisition.requested_by_name}</td>
      <td>${Utils.formatDate(acquisition.created_at)}</td>
      <td>
        ${acquisition.status === 'pending' && isAdmin ? `
          <button class="btn btn-success btn-sm" onclick="approveAcquisition(${acquisition.id})">
            <i class="fas fa-check"></i> Approve
          </button>
          <button class="btn btn-danger btn-sm" onclick="rejectAcquisition(${acquisition.id})">
            <i class="fas fa-times"></i> Reject
          </button>
        ` : ''}
        ${acquisition.status === 'pending' && acquisition.requested_by === currentUser.id ? `
          <button class="btn btn-warning btn-sm" onclick="editAcquisition(${acquisition.id})">
            <i class="fas fa-edit"></i> Edit
          </button>
        ` : ''}
      </td>
    </tr>
  `).join('');
}

// Reservation functions
async function cancelReservation(id) {
  try {
    const response = await fetch(`/api/reservations/${id}/cancel`, {
      method: 'PUT'
    });

    if (response.ok) {
      Utils.showNotification('Reservation cancelled successfully', 'success');
      loadReservations();
    } else {
      const error = await response.json();
      Utils.showNotification(error.error || 'Failed to cancel reservation', 'error');
    }
  } catch (error) {
    console.error('Error cancelling reservation:', error);
    Utils.showNotification('Failed to cancel reservation', 'error');
  }
}

async function fulfillReservation(id) {
  try {
    const response = await fetch(`/api/reservations/${id}/fulfill`, {
      method: 'PUT'
    });

    if (response.ok) {
      Utils.showNotification('Reservation fulfilled successfully', 'success');
      loadReservations();
    } else {
      const error = await response.json();
      Utils.showNotification(error.error || 'Failed to fulfill reservation', 'error');
    }
  } catch (error) {
    console.error('Error fulfilling reservation:', error);
    Utils.showNotification('Failed to fulfill reservation', 'error');
  }
}

// Fine functions
async function payFine(id, amount) {
  const paymentAmount = prompt(`Enter payment amount (Outstanding: $${amount}):`);

  if (!paymentAmount || isNaN(paymentAmount) || parseFloat(paymentAmount) <= 0) {
    Utils.showNotification('Please enter a valid payment amount', 'error');
    return;
  }

  try {
    const response = await fetch(`/api/fines/${id}/pay`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ amount: parseFloat(paymentAmount) })
    });

    if (response.ok) {
      Utils.showNotification('Payment recorded successfully', 'success');
      loadFines();
    } else {
      const error = await response.json();
      Utils.showNotification(error.error || 'Failed to record payment', 'error');
    }
  } catch (error) {
    console.error('Error recording payment:', error);
    Utils.showNotification('Failed to record payment', 'error');
  }
}

async function waiveFine(id) {
  if (!confirm('Are you sure you want to waive this fine?')) {
    return;
  }

  try {
    const response = await fetch(`/api/fines/${id}/waive`, {
      method: 'POST'
    });

    if (response.ok) {
      Utils.showNotification('Fine waived successfully', 'success');
      loadFines();
    } else {
      const error = await response.json();
      Utils.showNotification(error.error || 'Failed to waive fine', 'error');
    }
  } catch (error) {
    console.error('Error waiving fine:', error);
    Utils.showNotification('Failed to waive fine', 'error');
  }
}

// Acquisition functions
async function approveAcquisition(id) {
  const quantity = prompt('Enter approved quantity:');

  if (!quantity || isNaN(quantity) || parseInt(quantity) <= 0) {
    Utils.showNotification('Please enter a valid quantity', 'error');
    return;
  }

  try {
    const response = await fetch(`/api/acquisitions/${id}/approve`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ quantity_approved: parseInt(quantity) })
    });

    if (response.ok) {
      Utils.showNotification('Acquisition request approved', 'success');
      loadAcquisitions();
    } else {
      const error = await response.json();
      Utils.showNotification(error.error || 'Failed to approve request', 'error');
    }
  } catch (error) {
    console.error('Error approving acquisition:', error);
    Utils.showNotification('Failed to approve request', 'error');
  }
}

async function rejectAcquisition(id) {
  const notes = prompt('Enter rejection reason (optional):');

  if (!confirm('Are you sure you want to reject this acquisition request?')) {
    return;
  }

  try {
    const response = await fetch(`/api/acquisitions/${id}/reject`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ notes })
    });

    if (response.ok) {
      Utils.showNotification('Acquisition request rejected', 'success');
      loadAcquisitions();
    } else {
      const error = await response.json();
      Utils.showNotification(error.error || 'Failed to reject request', 'error');
    }
  } catch (error) {
    console.error('Error rejecting acquisition:', error);
    Utils.showNotification('Failed to reject request', 'error');
  }
}

// Export additional functions
window.returnBook = returnBook;
window.deleteIssue = deleteIssue;
window.confirmDelete = confirmDelete;
window.reserveBook = (id) => showModal('reserveBook', id);
window.showReserveBookModal = () => showModal('reserveBook');
window.showAcquisitionRequestModal = () => showModal('acquisitionRequest');
window.cancelReservation = cancelReservation;
window.fulfillReservation = fulfillReservation;
window.payFine = payFine;
window.waiveFine = waiveFine;
window.approveAcquisition = approveAcquisition;
window.rejectAcquisition = rejectAcquisition;
window.editAcquisition = (id) => showModal('editAcquisition', id);

// Reports functions
window.switchReportTab = switchReportTab;
window.generateUserReport = generateUserReport;
window.generateBookReport = generateBookReport;
window.generateIssueReport = generateIssueReport;

// Reports Functions Implementation
function switchReportTab(tabElement, reportType) {
  // Update active tab
  document.querySelectorAll('.report-tab').forEach(tab => tab.classList.remove('active'));
  tabElement.classList.add('active');

  // Update active content
  document.querySelectorAll('.report-content').forEach(content => content.classList.remove('active'));
  document.getElementById(`${reportType}Report`).classList.add('active');

  // Load report data based on type
  switch (reportType) {
    case 'summary':
      loadSummaryReport();
      break;
    case 'users':
      loadUserReportData();
      break;
    case 'books':
      loadBookReportData();
      break;
    case 'issues':
      loadIssueReportData();
      break;
    case 'analytics':
      loadAnalyticsData();
      break;
  }
}

async function loadSummaryReport() {
  try {
    showLoading(true);
    const summary = await ReportsAPI.getSummaryReport();

    // Update summary stats
    document.getElementById('totalUsers').textContent = summary.userStats.total_users || 0;
    document.getElementById('totalBooks').textContent = summary.bookStats.total_books || 0;
    document.getElementById('totalIssues').textContent = summary.issueStats.total_issues || 0;
    document.getElementById('overdueIssues').textContent = summary.issueStats.overdue_issues || 0;

    // Update charts
    updateUserDistributionChart(summary.userStats);
    updateBookStatusChart(summary.bookStats);

  } catch (error) {
    console.error('Error loading summary report:', error);
    Utils.showNotification('Error loading summary report', 'error');
  } finally {
    showLoading(false);
  }
}

function updateUserDistributionChart(userStats) {
  const chartContainer = document.getElementById('userDistributionChart');
  chartContainer.innerHTML = `
    <div class="chart-placeholder">
      <div class="chart-item">
        <span class="chart-label">Students:</span>
        <span class="chart-value">${userStats.total_students || 0}</span>
      </div>
      <div class="chart-item">
        <span class="chart-label">Admins:</span>
        <span class="chart-value">${userStats.total_admins || 0}</span>
      </div>
      <div class="chart-item">
        <span class="chart-label">Active:</span>
        <span class="chart-value">${userStats.active_users || 0}</span>
      </div>
      <div class="chart-item">
        <span class="chart-label">Blocked:</span>
        <span class="chart-value">${userStats.blocked_users || 0}</span>
      </div>
    </div>
  `;
}

function updateBookStatusChart(bookStats) {
  const chartContainer = document.getElementById('bookStatusChart');
  chartContainer.innerHTML = `
    <div class="chart-placeholder">
      <div class="chart-item">
        <span class="chart-label">Total Copies:</span>
        <span class="chart-value">${bookStats.total_copies || 0}</span>
      </div>
      <div class="chart-item">
        <span class="chart-label">Available:</span>
        <span class="chart-value">${bookStats.available_copies || 0}</span>
      </div>
      <div class="chart-item">
        <span class="chart-label">Issued:</span>
        <span class="chart-value">${bookStats.issued_copies || 0}</span>
      </div>
      <div class="chart-item">
        <span class="chart-label">Categories:</span>
        <span class="chart-value">${bookStats.total_categories || 0}</span>
      </div>
    </div>
  `;
}

async function loadUserReportData() {
  // Data will be loaded when generate button is clicked
}

async function loadBookReportData() {
  try {
    // Load categories for filter
    const books = await BooksAPI.getAll();
    const categories = [...new Set(books.map(book => book.category).filter(Boolean))];

    const categoryFilter = document.getElementById('bookCategoryFilter');
    categoryFilter.innerHTML = '<option value="">All Categories</option>';
    categories.forEach(category => {
      categoryFilter.innerHTML += `<option value="${category}">${category}</option>`;
    });
  } catch (error) {
    console.error('Error loading book report data:', error);
  }
}

async function loadIssueReportData() {
  try {
    // Set default date range (last 30 days)
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30);

    document.getElementById('issueStartDate').value = startDate.toISOString().split('T')[0];
    document.getElementById('issueEndDate').value = endDate.toISOString().split('T')[0];
  } catch (error) {
    console.error('Error loading issue report data:', error);
  }
}

async function loadAnalyticsData() {
  try {
    showLoading(true);

    // Load popular books
    const popularBooks = await ReportsAPI.getPopularBooksReport();
    updatePopularBooksChart(popularBooks);

    // Load overdue analysis
    const overdueData = await ReportsAPI.getOverdueReport();
    updateOverdueAnalysisChart(overdueData);

  } catch (error) {
    console.error('Error loading analytics data:', error);
    Utils.showNotification('Error loading analytics data', 'error');
  } finally {
    showLoading(false);
  }
}

function updatePopularBooksChart(popularBooks) {
  const chartContainer = document.getElementById('popularBooksChart');
  if (popularBooks.length === 0) {
    chartContainer.innerHTML = '<p>No data available</p>';
    return;
  }

  chartContainer.innerHTML = `
    <div class="popular-books-list">
      ${popularBooks.slice(0, 5).map((book, index) => `
        <div class="popular-book-item">
          <span class="rank">${index + 1}.</span>
          <div class="book-info">
            <strong>${book.title}</strong>
            <small>${book.author}</small>
          </div>
          <span class="issue-count">${book.issue_count} issues</span>
        </div>
      `).join('')}
    </div>
  `;
}

function updateOverdueAnalysisChart(overdueData) {
  const chartContainer = document.getElementById('overdueAnalysisChart');
  if (overdueData.length === 0) {
    chartContainer.innerHTML = '<p>No overdue books</p>';
    return;
  }

  const totalOverdue = overdueData.length;
  const avgOverdueDays = overdueData.reduce((sum, item) => sum + item.days_overdue, 0) / totalOverdue;

  chartContainer.innerHTML = `
    <div class="overdue-stats">
      <div class="overdue-stat">
        <h4>${totalOverdue}</h4>
        <p>Overdue Books</p>
      </div>
      <div class="overdue-stat">
        <h4>${Math.round(avgOverdueDays)}</h4>
        <p>Avg Days Overdue</p>
      </div>
    </div>
  `;
}

// Report Generation Functions
async function generateUserReport() {
  try {
    showLoading(true);
    const format = document.getElementById('userReportFormat').value;
    const users = await ReportsAPI.getUserReport();

    if (format === 'table') {
      renderUserReportTable(users);
    } else if (format === 'csv') {
      downloadUserReportCSV(users);
    } else if (format === 'pdf') {
      generateUserReportPDF(users);
    }

  } catch (error) {
    console.error('Error generating user report:', error);
    Utils.showNotification('Error generating user report', 'error');
  } finally {
    showLoading(false);
  }
}

async function generateBookReport() {
  try {
    showLoading(true);
    const format = document.getElementById('bookReportFormat').value;
    const category = document.getElementById('bookCategoryFilter').value;

    let books = await ReportsAPI.getBookReport();

    // Filter by category if selected
    if (category) {
      books = books.filter(book => book.category === category);
    }

    if (format === 'table') {
      renderBookReportTable(books);
    } else if (format === 'csv') {
      downloadBookReportCSV(books);
    } else if (format === 'pdf') {
      generateBookReportPDF(books);
    }

  } catch (error) {
    console.error('Error generating book report:', error);
    Utils.showNotification('Error generating book report', 'error');
  } finally {
    showLoading(false);
  }
}

async function generateIssueReport() {
  try {
    showLoading(true);
    const format = document.getElementById('issueReportFormat').value;
    const filters = {
      start_date: document.getElementById('issueStartDate').value,
      end_date: document.getElementById('issueEndDate').value,
      status: document.getElementById('issueStatusFilter').value
    };

    const issues = await ReportsAPI.getIssueReport(filters);

    if (format === 'table') {
      renderIssueReportTable(issues);
    } else if (format === 'csv') {
      downloadIssueReportCSV(issues);
    } else if (format === 'pdf') {
      generateIssueReportPDF(issues);
    }

  } catch (error) {
    console.error('Error generating issue report:', error);
    Utils.showNotification('Error generating issue report', 'error');
  } finally {
    showLoading(false);
  }
}

// Report Table Rendering Functions
function renderUserReportTable(users) {
  const container = document.getElementById('userReportTable');

  if (users.length === 0) {
    container.innerHTML = '<p class="text-center">No users found</p>';
    return;
  }

  container.innerHTML = `
    <table class="report-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Email</th>
          <th>Type</th>
          <th>Student ID</th>
          <th>Status</th>
          <th>Books Issued</th>
          <th>Currently Issued</th>
          <th>Overdue Books</th>
          <th>Total Fines</th>
          <th>Unpaid Fines</th>
        </tr>
      </thead>
      <tbody>
        ${users.map(user => `
          <tr>
            <td>${user.full_name}</td>
            <td>${user.email}</td>
            <td>${user.user_type.replace('_', ' ')}</td>
            <td>${user.student_id || 'N/A'}</td>
            <td>
              ${!user.is_active ? 'Inactive' : user.is_blocked ? 'Blocked' : 'Active'}
            </td>
            <td>${user.total_books_issued || 0}</td>
            <td>${user.currently_issued || 0}</td>
            <td>${user.overdue_books || 0}</td>
            <td>$${user.total_fines || '0.00'}</td>
            <td>$${user.unpaid_fines || '0.00'}</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;
}

function renderBookReportTable(books) {
  const container = document.getElementById('bookReportTable');

  if (books.length === 0) {
    container.innerHTML = '<p class="text-center">No books found</p>';
    return;
  }

  container.innerHTML = `
    <table class="report-table">
      <thead>
        <tr>
          <th>Title</th>
          <th>Author</th>
          <th>ISBN</th>
          <th>Category</th>
          <th>Total Copies</th>
          <th>Available</th>
          <th>Times Issued</th>
          <th>Currently Issued</th>
          <th>Avg Duration</th>
          <th>Last Issued</th>
        </tr>
      </thead>
      <tbody>
        ${books.map(book => `
          <tr>
            <td>${book.title}</td>
            <td>${book.author}</td>
            <td>${book.isbn || 'N/A'}</td>
            <td>${book.category || 'Uncategorized'}</td>
            <td>${book.total_copies}</td>
            <td>${book.available_copies}</td>
            <td>${book.total_times_issued || 0}</td>
            <td>${book.currently_issued || 0}</td>
            <td>${book.avg_issue_duration ? Math.round(book.avg_issue_duration) + ' days' : 'N/A'}</td>
            <td>${book.last_issued_date ? Utils.formatDate(book.last_issued_date) : 'Never'}</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;
}

function renderIssueReportTable(issues) {
  const container = document.getElementById('issueReportTable');

  if (issues.length === 0) {
    container.innerHTML = '<p class="text-center">No issues found</p>';
    return;
  }

  container.innerHTML = `
    <table class="report-table">
      <thead>
        <tr>
          <th>Book</th>
          <th>User</th>
          <th>Issue Date</th>
          <th>Due Date</th>
          <th>Return Date</th>
          <th>Status</th>
          <th>Days Issued</th>
          <th>Days Overdue</th>
          <th>Fine Amount</th>
        </tr>
      </thead>
      <tbody>
        ${issues.map(issue => `
          <tr>
            <td>
              <strong>${issue.book_title}</strong><br>
              <small>${issue.book_author}</small>
            </td>
            <td>
              <strong>${issue.user_name}</strong><br>
              <small>${issue.student_id || 'N/A'}</small>
            </td>
            <td>${Utils.formatDate(issue.issue_date)}</td>
            <td>${Utils.formatDate(issue.due_date)}</td>
            <td>${issue.return_date ? Utils.formatDate(issue.return_date) : 'Not returned'}</td>
            <td>
              <span class="status-badge status-${issue.status}">
                ${issue.status}
              </span>
            </td>
            <td>${Math.round(issue.days_issued)} days</td>
            <td>${issue.days_overdue > 0 ? Math.round(issue.days_overdue) + ' days' : 'N/A'}</td>
            <td>$${issue.fine_amount || '0.00'}</td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  `;
}

// CSV Download Functions
function downloadUserReportCSV(users) {
  const headers = ['Name', 'Email', 'Type', 'Student ID', 'Status', 'Books Issued', 'Currently Issued', 'Overdue Books', 'Total Fines', 'Unpaid Fines'];

  const csvContent = [
    headers.join(','),
    ...users.map(user => [
      `"${user.full_name}"`,
      `"${user.email}"`,
      `"${user.user_type.replace('_', ' ')}"`,
      `"${user.student_id || 'N/A'}"`,
      `"${!user.is_active ? 'Inactive' : user.is_blocked ? 'Blocked' : 'Active'}"`,
      user.total_books_issued || 0,
      user.currently_issued || 0,
      user.overdue_books || 0,
      user.total_fines || 0,
      user.unpaid_fines || 0
    ].join(','))
  ].join('\n');

  downloadCSV(csvContent, 'user-report.csv');
}

function downloadBookReportCSV(books) {
  const headers = ['Title', 'Author', 'ISBN', 'Category', 'Total Copies', 'Available', 'Times Issued', 'Currently Issued', 'Avg Duration', 'Last Issued'];

  const csvContent = [
    headers.join(','),
    ...books.map(book => [
      `"${book.title}"`,
      `"${book.author}"`,
      `"${book.isbn || 'N/A'}"`,
      `"${book.category || 'Uncategorized'}"`,
      book.total_copies,
      book.available_copies,
      book.total_times_issued || 0,
      book.currently_issued || 0,
      book.avg_issue_duration ? Math.round(book.avg_issue_duration) : 0,
      `"${book.last_issued_date ? Utils.formatDate(book.last_issued_date) : 'Never'}"`
    ].join(','))
  ].join('\n');

  downloadCSV(csvContent, 'book-report.csv');
}

function downloadIssueReportCSV(issues) {
  const headers = ['Book Title', 'Book Author', 'User Name', 'Student ID', 'Issue Date', 'Due Date', 'Return Date', 'Status', 'Days Issued', 'Days Overdue', 'Fine Amount'];

  const csvContent = [
    headers.join(','),
    ...issues.map(issue => [
      `"${issue.book_title}"`,
      `"${issue.book_author}"`,
      `"${issue.user_name}"`,
      `"${issue.student_id || 'N/A'}"`,
      `"${Utils.formatDate(issue.issue_date)}"`,
      `"${Utils.formatDate(issue.due_date)}"`,
      `"${issue.return_date ? Utils.formatDate(issue.return_date) : 'Not returned'}"`,
      `"${issue.status}"`,
      Math.round(issue.days_issued),
      issue.days_overdue > 0 ? Math.round(issue.days_overdue) : 0,
      issue.fine_amount || 0
    ].join(','))
  ].join('\n');

  downloadCSV(csvContent, 'issue-report.csv');
}

function downloadCSV(content, filename) {
  const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');

  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    Utils.showNotification(`${filename} downloaded successfully`, 'success');
  }
}

// PDF Generation Functions (placeholder - would need a PDF library like jsPDF)
function generateUserReportPDF(users) {
  Utils.showNotification('PDF generation feature coming soon', 'info');
}

function generateBookReportPDF(books) {
  Utils.showNotification('PDF generation feature coming soon', 'info');
}

function generateIssueReportPDF(issues) {
  Utils.showNotification('PDF generation feature coming soon', 'info');
}
