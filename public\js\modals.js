// Modal Management System
class ModalManager {
  constructor() {
    this.activeModal = null;
    this.modalContainer = document.getElementById('modalContainer');
    this.initializeEventListeners();
  }
  
  initializeEventListeners() {
    // Close modal on escape key
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Escape' && this.activeModal) {
        this.closeModal();
      }
    });
  }
  
  showModal(modalType, data = null) {
    this.closeModal(); // Close any existing modal
    
    const modalHTML = this.getModalHTML(modalType, data);
    this.modalContainer.innerHTML = modalHTML;
    
    const modal = this.modalContainer.querySelector('.modal');
    if (modal) {
      this.activeModal = modal;
      modal.classList.add('show');
      
      // Initialize modal-specific functionality
      this.initializeModal(modalType, data);
      
      // Focus first input
      const firstInput = modal.querySelector('input, select, textarea');
      if (firstInput) {
        setTimeout(() => firstInput.focus(), 100);
      }
    }
  }
  
  closeModal() {
    if (this.activeModal) {
      this.activeModal.classList.remove('show');
      setTimeout(() => {
        this.modalContainer.innerHTML = '';
        this.activeModal = null;
      }, 300);
    }
  }
  
  getModalHTML(modalType, data) {
    switch (modalType) {
      case 'addBook':
      case 'editBook':
        return this.getBookModalHTML(modalType === 'editBook', data);
      case 'addUser':
      case 'editUser':
        return this.getUserModalHTML(modalType === 'editUser', data);
      case 'issueBook':
        return this.getIssueBookModalHTML();
      case 'reserveBook':
        return this.getReserveBookModalHTML();
      case 'profile':
        return this.getProfileModalHTML();
      case 'changePassword':
        return this.getChangePasswordModalHTML();
      default:
        return '';
    }
  }
  
  getBookModalHTML(isEdit, bookId) {
    return `
      <div class="modal add-book-modal">
        <div class="modal-content">
          <div class="modal-header">
            <i class="fas fa-book"></i>
            <h3>${isEdit ? 'Edit Book' : 'Add New Book'}</h3>
            <button class="modal-close" onclick="modalManager.closeModal()">&times;</button>
          </div>
          <div class="modal-body">
            <form class="modal-form" id="bookForm">
              <div class="form-row">
                <div class="form-group">
                  <label for="bookTitle">
                    <i class="fas fa-heading"></i>
                    Title <span class="required">*</span>
                  </label>
                  <input type="text" id="bookTitle" class="form-input" required>
                  <div class="error-message" id="titleError"></div>
                </div>
                <div class="form-group">
                  <label for="bookAuthor">
                    <i class="fas fa-user-edit"></i>
                    Author <span class="required">*</span>
                  </label>
                  <input type="text" id="bookAuthor" class="form-input" required>
                  <div class="error-message" id="authorError"></div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="bookISBN">
                    <i class="fas fa-barcode"></i>
                    ISBN
                  </label>
                  <input type="text" id="bookISBN" class="form-input">
                  <div class="error-message" id="isbnError"></div>
                </div>
                <div class="form-group">
                  <label for="bookYear">
                    <i class="fas fa-calendar"></i>
                    Published Year
                  </label>
                  <input type="number" id="bookYear" class="form-input" min="1000" max="2030">
                  <div class="error-message" id="yearError"></div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="bookCategory">
                    <i class="fas fa-tags"></i>
                    Category
                  </label>
                  <select id="bookCategory" class="form-select">
                    <option value="">Select Category</option>
                    <option value="Fiction">Fiction</option>
                    <option value="Non-Fiction">Non-Fiction</option>
                    <option value="Science">Science</option>
                    <option value="Technology">Technology</option>
                    <option value="History">History</option>
                    <option value="Biography">Biography</option>
                    <option value="Education">Education</option>
                    <option value="Reference">Reference</option>
                  </select>
                </div>
                <div class="form-group">
                  <label for="bookCopies">
                    <i class="fas fa-copy"></i>
                    Total Copies
                  </label>
                  <input type="number" id="bookCopies" class="form-input" min="1" value="1">
                </div>
              </div>
              
              <div class="form-group">
                <label for="bookDescription">
                  <i class="fas fa-align-left"></i>
                  Description
                </label>
                <textarea id="bookDescription" class="form-textarea" placeholder="Enter book description..."></textarea>
              </div>
              
              <div class="form-group">
                <label>
                  <i class="fas fa-image"></i>
                  Book Cover
                </label>
                <div class="file-upload" id="coverUpload">
                  <input type="file" class="file-upload-input" id="bookCover" accept="image/*">
                  <label for="bookCover" class="file-upload-label">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <span>Click to upload or drag and drop</span>
                  </label>
                  <div class="file-upload-preview" id="coverPreview"></div>
                </div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">Cancel</button>
            <button type="submit" class="btn btn-primary" onclick="saveBook(${isEdit}, ${bookId || null})">
              <i class="fas fa-save"></i>
              ${isEdit ? 'Update Book' : 'Add Book'}
            </button>
          </div>
        </div>
      </div>
    `;
  }
  
  getUserModalHTML(isEdit, userId) {
    return `
      <div class="modal add-user-modal">
        <div class="modal-content">
          <div class="modal-header">
            <i class="fas fa-user-plus"></i>
            <h3>${isEdit ? 'Edit User' : 'Add New User'}</h3>
            <button class="modal-close" onclick="modalManager.closeModal()">&times;</button>
          </div>
          <div class="modal-body">
            <form class="modal-form" id="userForm">
              <div class="form-row">
                <div class="form-group">
                  <label for="userFullName">
                    <i class="fas fa-user"></i>
                    Full Name <span class="required">*</span>
                  </label>
                  <input type="text" id="userFullName" class="form-input" required>
                  <div class="error-message" id="fullNameError"></div>
                </div>
                <div class="form-group">
                  <label for="userEmail">
                    <i class="fas fa-envelope"></i>
                    Email <span class="required">*</span>
                  </label>
                  <input type="email" id="userEmail" class="form-input" required>
                  <div class="error-message" id="emailError"></div>
                </div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="userPhone">
                    <i class="fas fa-phone"></i>
                    Phone Number
                  </label>
                  <input type="tel" id="userPhone" class="form-input">
                </div>
                <div class="form-group">
                  <label for="userType">
                    <i class="fas fa-user-tag"></i>
                    User Type <span class="required">*</span>
                  </label>
                  <select id="userType" class="form-select" required onchange="toggleStudentId()">
                    <option value="">Select Type</option>
                    <option value="library_admin">Library Admin</option>
                    <option value="student">Student</option>
                  </select>
                </div>
              </div>
              
              <div class="form-group" id="studentIdGroup" style="display: none;">
                <label for="userStudentId">
                  <i class="fas fa-id-card"></i>
                  Student ID <span class="required">*</span>
                </label>
                <input type="text" id="userStudentId" class="form-input">
                <div class="error-message" id="studentIdError"></div>
              </div>

              <div class="form-group">
                <label for="userStatus">
                  <i class="fas fa-toggle-on"></i>
                  Status <span class="required">*</span>
                </label>
                <select id="userStatus" class="form-select" required>
                  <option value="1">Active</option>
                  <option value="0">Inactive</option>
                </select>
              </div>
              
              ${!isEdit ? `
                <div class="form-group">
                  <label for="userPassword">
                    <i class="fas fa-lock"></i>
                    Password <span class="required">*</span>
                  </label>
                  <input type="password" id="userPassword" class="form-input" required>
                  <div class="error-message" id="passwordError"></div>
                </div>
              ` : ''}
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">Cancel</button>
            <button type="submit" class="btn btn-primary" onclick="saveUser(${isEdit}, ${userId || null})">
              <i class="fas fa-save"></i>
              ${isEdit ? 'Update User' : 'Add User'}
            </button>
          </div>
        </div>
      </div>
    `;
  }
  
  getIssueBookModalHTML() {
    return `
      <div class="modal issue-book-modal">
        <div class="modal-content">
          <div class="modal-header">
            <i class="fas fa-hand-holding"></i>
            <h3>Issue Book</h3>
            <button class="modal-close" onclick="modalManager.closeModal()">&times;</button>
          </div>
          <div class="modal-body">
            <form class="modal-form" id="issueForm">
              <div class="form-group">
                <label for="issueStudent">
                  <i class="fas fa-user-graduate"></i>
                  Select Student <span class="required">*</span>
                </label>
                <div class="search-select">
                  <input type="text" id="issueStudent" class="search-select-input" placeholder="Search student by name or ID..." required>
                  <div class="search-select-dropdown" id="studentDropdown"></div>
                </div>
                <div class="error-message" id="studentError"></div>
              </div>
              
              <div class="form-group">
                <label for="issueBook">
                  <i class="fas fa-book"></i>
                  Select Book <span class="required">*</span>
                </label>
                <div class="search-select">
                  <input type="text" id="issueBook" class="search-select-input" placeholder="Search available books..." required>
                  <div class="search-select-dropdown" id="bookDropdown"></div>
                </div>
                <div class="error-message" id="bookError"></div>
              </div>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="issueDate">
                    <i class="fas fa-calendar-plus"></i>
                    Issue Date
                  </label>
                  <input type="date" id="issueDate" class="date-input" readonly>
                </div>
                <div class="form-group">
                  <label for="dueDate">
                    <i class="fas fa-calendar-times"></i>
                    Due Date <span class="required">*</span>
                  </label>
                  <input type="date" id="dueDate" class="date-input" required>
                </div>
              </div>
              
              <div class="form-group">
                <label for="issueNotes">
                  <i class="fas fa-sticky-note"></i>
                  Notes
                </label>
                <textarea id="issueNotes" class="form-textarea" placeholder="Any additional notes..."></textarea>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">Cancel</button>
            <button type="submit" class="btn btn-primary" onclick="saveIssue()">
              <i class="fas fa-hand-holding"></i>
              Issue Book
            </button>
          </div>
        </div>
      </div>
    `;
  }
  
  getProfileModalHTML() {
    return `
      <div class="modal profile-modal">
        <div class="modal-content">
          <div class="modal-header">
            <i class="fas fa-user-edit"></i>
            <h3>Edit Profile</h3>
            <button class="modal-close" onclick="modalManager.closeModal()">&times;</button>
          </div>
          <div class="modal-body">
            <form class="modal-form" id="profileForm">
              <div class="form-group">
                <label for="profileFullName">
                  <i class="fas fa-user"></i>
                  Full Name <span class="required">*</span>
                </label>
                <input type="text" id="profileFullName" class="form-input" required>
              </div>
              
              <div class="form-group">
                <label for="profileEmail">
                  <i class="fas fa-envelope"></i>
                  Email
                </label>
                <input type="email" id="profileEmail" class="form-input" readonly>
              </div>
              
              <div class="form-group">
                <label for="profilePhone">
                  <i class="fas fa-phone"></i>
                  Phone Number
                </label>
                <input type="tel" id="profilePhone" class="form-input">
              </div>
              
              <div class="form-group">
                <label for="profileUserType">
                  <i class="fas fa-user-tag"></i>
                  User Type
                </label>
                <input type="text" id="profileUserType" class="form-input" readonly>
              </div>
              
              <div class="form-group" id="profileStudentIdGroup" style="display: none;">
                <label for="profileStudentId">
                  <i class="fas fa-id-card"></i>
                  Student ID
                </label>
                <input type="text" id="profileStudentId" class="form-input" readonly>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">Cancel</button>
            <button type="submit" class="btn btn-primary" onclick="saveProfile()">
              <i class="fas fa-save"></i>
              Update Profile
            </button>
          </div>
        </div>
      </div>
    `;
  }
  
  getChangePasswordModalHTML() {
    return `
      <div class="modal change-password-modal">
        <div class="modal-content">
          <div class="modal-header">
            <i class="fas fa-key"></i>
            <h3>Change Password</h3>
            <button class="modal-close" onclick="modalManager.closeModal()">&times;</button>
          </div>
          <div class="modal-body">
            <form class="modal-form" id="passwordForm">
              <div class="form-group">
                <label for="currentPassword">
                  <i class="fas fa-lock"></i>
                  Current Password <span class="required">*</span>
                </label>
                <input type="password" id="currentPassword" class="form-input" required>
                <div class="error-message" id="currentPasswordError"></div>
              </div>
              
              <div class="form-group">
                <label for="newPassword">
                  <i class="fas fa-key"></i>
                  New Password <span class="required">*</span>
                </label>
                <input type="password" id="newPassword" class="form-input" required minlength="6">
                <div class="error-message" id="newPasswordError"></div>
              </div>
              
              <div class="form-group">
                <label for="confirmPassword">
                  <i class="fas fa-check-circle"></i>
                  Confirm New Password <span class="required">*</span>
                </label>
                <input type="password" id="confirmPassword" class="form-input" required>
                <div class="error-message" id="confirmPasswordError"></div>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">Cancel</button>
            <button type="submit" class="btn btn-primary" onclick="savePassword()">
              <i class="fas fa-save"></i>
              Change Password
            </button>
          </div>
        </div>
      </div>
    `;
  }

  getReserveBookModalHTML() {
    return `
      <div class="modal reserve-book-modal">
        <div class="modal-content">
          <div class="modal-header">
            <i class="fas fa-bookmark"></i>
            <h3>Reserve Book</h3>
            <button class="modal-close" onclick="modalManager.closeModal()">&times;</button>
          </div>
          <div class="modal-body">
            <form class="modal-form" id="reserveBookForm">
              <div class="form-group">
                <label for="reserveBookSearch">
                  <i class="fas fa-book"></i>
                  Select Book <span class="required">*</span>
                </label>
                <div class="search-select">
                  <input type="text" id="reserveBookSearch" class="search-select-input" placeholder="Search books..." required>
                  <div class="search-select-dropdown" id="reserveBookDropdown"></div>
                </div>
                <div class="error-message" id="reserveBookError"></div>
              </div>

              <div class="form-group">
                <label for="reservePriority">
                  <i class="fas fa-flag"></i>
                  Priority <span class="required">*</span>
                </label>
                <select id="reservePriority" class="form-select" required>
                  <option value="">Select Priority</option>
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
                <div class="error-message" id="priorityError"></div>
              </div>

              <div class="form-group">
                <label for="reserveNotes">
                  <i class="fas fa-sticky-note"></i>
                  Notes (Optional)
                </label>
                <textarea id="reserveNotes" class="form-textarea" rows="3" placeholder="Why do you need this book? Any specific requirements..."></textarea>
              </div>
            </form>
          </div>
          <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="modalManager.closeModal()">Cancel</button>
            <button type="submit" class="btn btn-primary" onclick="saveReservation()">
              <i class="fas fa-bookmark"></i>
              Reserve Book
            </button>
          </div>
        </div>
      </div>
    `;
  }

  async initializeModal(modalType, data) {
    switch (modalType) {
      case 'editBook':
        await this.loadBookData(data);
        break;
      case 'editUser':
        await this.loadUserData(data);
        break;
      case 'issueBook':
        this.initializeIssueModal();
        break;
      case 'reserveBook':
        this.initializeReserveModal();
        break;
      case 'profile':
        await this.loadProfileData();
        break;
    }
    
    // Initialize file upload if present
    this.initializeFileUpload();
    
    // Initialize search selects if present
    this.initializeSearchSelects();
  }
  
  async loadBookData(bookId) {
    try {
      const book = await BooksAPI.getById(bookId);
      
      document.getElementById('bookTitle').value = book.title || '';
      document.getElementById('bookAuthor').value = book.author || '';
      document.getElementById('bookISBN').value = book.isbn || '';
      document.getElementById('bookYear').value = book.published_year || '';
      document.getElementById('bookCategory').value = book.category || '';
      document.getElementById('bookCopies').value = book.total_copies || 1;
      document.getElementById('bookDescription').value = book.description || '';
      
      if (book.cover_image) {
        const preview = document.getElementById('coverPreview');
        preview.innerHTML = `<img src="${book.cover_image}" alt="Book Cover">`;
      }
      
    } catch (error) {
      console.error('Error loading book data:', error);
      Utils.showNotification('Error loading book data', 'error');
    }
  }
  
  async loadUserData(userId) {
    try {
      const user = await UsersAPI.getById(userId);
      
      document.getElementById('userFullName').value = user.full_name || '';
      document.getElementById('userEmail').value = user.email || '';
      document.getElementById('userPhone').value = user.phone || '';
      document.getElementById('userType').value = user.user_type || '';
      document.getElementById('userStatus').value = user.is_active ? '1' : '0';

      if (user.user_type === 'student') {
        document.getElementById('studentIdGroup').style.display = 'block';
        document.getElementById('userStudentId').value = user.student_id || '';
      }
      
    } catch (error) {
      console.error('Error loading user data:', error);
      Utils.showNotification('Error loading user data', 'error');
    }
  }
  
  async loadProfileData() {
    try {
      const user = await UsersAPI.getProfile();
      
      document.getElementById('profileFullName').value = user.full_name || '';
      document.getElementById('profileEmail').value = user.email || '';
      document.getElementById('profilePhone').value = user.phone || '';
      document.getElementById('profileUserType').value = (user.user_type || '').replace('_', ' ');
      
      if (user.user_type === 'student') {
        document.getElementById('profileStudentIdGroup').style.display = 'block';
        document.getElementById('profileStudentId').value = user.student_id || '';
      }
      
    } catch (error) {
      console.error('Error loading profile data:', error);
      Utils.showNotification('Error loading profile data', 'error');
    }
  }
  
  initializeIssueModal() {
    // Set default dates
    const today = new Date();
    const dueDate = new Date(today);
    dueDate.setDate(today.getDate() + 14); // 14 days from today

    document.getElementById('issueDate').value = today.toISOString().split('T')[0];
    document.getElementById('dueDate').value = dueDate.toISOString().split('T')[0];
  }

  initializeReserveModal() {
    // Initialize book search for reservations
    const bookInput = document.getElementById('reserveBookSearch');
    const bookDropdown = document.getElementById('reserveBookDropdown');

    if (bookInput) {
      this.initializeReserveBookSearch(bookInput, bookDropdown);
    }
  }
  
  initializeFileUpload() {
    const fileInput = document.getElementById('bookCover');
    const fileUpload = document.getElementById('coverUpload');
    const preview = document.getElementById('coverPreview');
    
    if (!fileInput) return;
    
    fileInput.addEventListener('change', (e) => {
      const file = e.target.files[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          preview.innerHTML = `<img src="${e.target.result}" alt="Book Cover Preview">`;
        };
        reader.readAsDataURL(file);
      }
    });
    
    // Drag and drop functionality
    fileUpload.addEventListener('dragover', (e) => {
      e.preventDefault();
      fileUpload.classList.add('dragover');
    });
    
    fileUpload.addEventListener('dragleave', () => {
      fileUpload.classList.remove('dragover');
    });
    
    fileUpload.addEventListener('drop', (e) => {
      e.preventDefault();
      fileUpload.classList.remove('dragover');
      
      const files = e.dataTransfer.files;
      if (files.length > 0) {
        fileInput.files = files;
        fileInput.dispatchEvent(new Event('change'));
      }
    });
  }
  
  initializeSearchSelects() {
    // Initialize student search
    const studentInput = document.getElementById('issueStudent');
    const studentDropdown = document.getElementById('studentDropdown');
    
    if (studentInput) {
      this.initializeStudentSearch(studentInput, studentDropdown);
    }
    
    // Initialize book search
    const bookInput = document.getElementById('issueBook');
    const bookDropdown = document.getElementById('bookDropdown');
    
    if (bookInput) {
      this.initializeBookSearch(bookInput, bookDropdown);
    }
  }
  
  async initializeStudentSearch(input, dropdown) {
    let students = [];

    try {
      students = await UsersAPI.getStudents();
      console.log('Loaded students:', students); // Debug log
    } catch (error) {
      console.error('Error loading students:', error);
      return;
    }

    // Show all students when input is clicked/focused
    const showAllStudents = () => {
      if (students.length === 0) {
        dropdown.innerHTML = '<div class="search-select-option">No students found</div>';
        dropdown.classList.add('show');
        return;
      }

      this.renderSearchOptions(dropdown, students, (student) => {
        input.value = `${student.full_name} (${student.student_id})`;
        input.dataset.userId = student.id;
        dropdown.classList.remove('show');
      }, (student) => `${student.full_name} (${student.student_id})`);
    };

    // Filter students as user types
    input.addEventListener('input', () => {
      const searchTerm = input.value.toLowerCase().trim();

      if (searchTerm === '') {
        // Show all students if search is empty
        showAllStudents();
        return;
      }

      const filteredStudents = students.filter(student =>
        student.full_name.toLowerCase().includes(searchTerm) ||
        (student.student_id && student.student_id.toLowerCase().includes(searchTerm)) ||
        (student.email && student.email.toLowerCase().includes(searchTerm))
      );

      if (filteredStudents.length === 0) {
        dropdown.innerHTML = '<div class="search-select-option">No students found</div>';
        dropdown.classList.add('show');
        return;
      }

      this.renderSearchOptions(dropdown, filteredStudents, (student) => {
        input.value = `${student.full_name} (${student.student_id})`;
        input.dataset.userId = student.id;
        dropdown.classList.remove('show');
      }, (student) => `${student.full_name} (${student.student_id})`);
    });

    // Show all students when input is focused/clicked
    input.addEventListener('focus', showAllStudents);
    input.addEventListener('click', showAllStudents);

    // Hide dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!input.contains(e.target) && !dropdown.contains(e.target)) {
        dropdown.classList.remove('show');
      }
    });
  }
  
  async initializeBookSearch(input, dropdown) {
    let books = [];

    try {
      books = await BooksAPI.getAvailable();
      console.log('Loaded available books:', books); // Debug log
    } catch (error) {
      console.error('Error loading books:', error);
      return;
    }

    // Show all available books when input is clicked/focused
    const showAllBooks = () => {
      if (books.length === 0) {
        dropdown.innerHTML = '<div class="search-select-option">No available books found</div>';
        dropdown.classList.add('show');
        return;
      }

      this.renderSearchOptions(dropdown, books, (book) => {
        input.value = `${book.title} by ${book.author}`;
        input.dataset.bookId = book.id;
        dropdown.classList.remove('show');
      }, (book) => `${book.title} by ${book.author} (Available: ${book.available_copies})`);
    };

    // Filter books as user types
    input.addEventListener('input', () => {
      const searchTerm = input.value.toLowerCase().trim();

      if (searchTerm === '') {
        // Show all books if search is empty
        showAllBooks();
        return;
      }

      const filteredBooks = books.filter(book =>
        book.title.toLowerCase().includes(searchTerm) ||
        book.author.toLowerCase().includes(searchTerm) ||
        (book.isbn && book.isbn.toLowerCase().includes(searchTerm)) ||
        (book.category && book.category.toLowerCase().includes(searchTerm))
      );

      if (filteredBooks.length === 0) {
        dropdown.innerHTML = '<div class="search-select-option">No books found</div>';
        dropdown.classList.add('show');
        return;
      }

      this.renderSearchOptions(dropdown, filteredBooks, (book) => {
        input.value = `${book.title} by ${book.author}`;
        input.dataset.bookId = book.id;
        dropdown.classList.remove('show');
      }, (book) => `${book.title} by ${book.author} (Available: ${book.available_copies})`);
    });

    // Show all books when input is focused/clicked
    input.addEventListener('focus', showAllBooks);
    input.addEventListener('click', showAllBooks);

    // Hide dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!input.contains(e.target) && !dropdown.contains(e.target)) {
        dropdown.classList.remove('show');
      }
    });
  }

  async initializeReserveBookSearch(input, dropdown) {
    let books = [];

    try {
      // Get all books for reservations (not just available ones)
      books = await BooksAPI.getAll();
      console.log('Loaded books for reservation:', books);
    } catch (error) {
      console.error('Error loading books:', error);
      return;
    }

    // Show all books when input is clicked/focused
    const showAllBooks = () => {
      if (books.length === 0) {
        dropdown.innerHTML = '<div class="search-select-option">No books found</div>';
        dropdown.classList.add('show');
        return;
      }

      this.renderSearchOptions(dropdown, books, (book) => {
        input.value = `${book.title} by ${book.author}`;
        input.dataset.bookId = book.id;
        dropdown.classList.remove('show');
      }, (book) => `${book.title} by ${book.author} (${book.available_copies > 0 ? 'Available' : 'Not Available'})`);
    };

    // Filter books as user types
    input.addEventListener('input', () => {
      const searchTerm = input.value.toLowerCase().trim();

      if (searchTerm === '') {
        showAllBooks();
        return;
      }

      const filteredBooks = books.filter(book =>
        book.title.toLowerCase().includes(searchTerm) ||
        book.author.toLowerCase().includes(searchTerm) ||
        (book.isbn && book.isbn.toLowerCase().includes(searchTerm)) ||
        (book.category && book.category.toLowerCase().includes(searchTerm))
      );

      if (filteredBooks.length === 0) {
        dropdown.innerHTML = '<div class="search-select-option">No books found</div>';
        dropdown.classList.add('show');
        return;
      }

      this.renderSearchOptions(dropdown, filteredBooks, (book) => {
        input.value = `${book.title} by ${book.author}`;
        input.dataset.bookId = book.id;
        dropdown.classList.remove('show');
      }, (book) => `${book.title} by ${book.author} (${book.available_copies > 0 ? 'Available' : 'Not Available'})`);
    });

    // Show all books when input is focused/clicked
    input.addEventListener('focus', showAllBooks);
    input.addEventListener('click', showAllBooks);

    // Hide dropdown when clicking outside
    document.addEventListener('click', (e) => {
      if (!input.contains(e.target) && !dropdown.contains(e.target)) {
        dropdown.classList.remove('show');
      }
    });
  }

  renderSearchOptions(dropdown, items, onSelect, displayFunction) {
    if (items.length === 0) {
      dropdown.innerHTML = '<div class="search-select-option no-results">No items found</div>';
      dropdown.classList.add('show');
      return;
    }

    // Limit display to first 10 items for performance, but show count if more
    const displayItems = items.slice(0, 10);
    const hasMore = items.length > 10;

    dropdown.innerHTML = displayItems.map(item => `
      <div class="search-select-option" data-value="${item.id}">
        ${displayFunction(item)}
      </div>
    `).join('') + (hasMore ? `<div class="search-select-option more-results">... and ${items.length - 10} more results</div>` : '');

    dropdown.classList.add('show');

    // Add click handlers only to selectable options
    dropdown.querySelectorAll('.search-select-option[data-value]').forEach(option => {
      option.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();

        const itemId = option.dataset.value;
        const item = items.find(i => i.id == itemId);
        if (item) {
          onSelect(item);
        }
      });

      // Add hover effect
      option.addEventListener('mouseenter', () => {
        dropdown.querySelectorAll('.search-select-option').forEach(opt => opt.classList.remove('highlighted'));
        option.classList.add('highlighted');
      });
    });
  }
}

// Initialize modal manager
const modalManager = new ModalManager();

// Global modal functions
window.showModal = (type, data) => modalManager.showModal(type, data);
window.modalManager = modalManager;

// Global utility functions for modals
window.toggleStudentId = () => {
  const userType = document.getElementById('userType').value;
  const studentIdGroup = document.getElementById('studentIdGroup');
  const studentIdInput = document.getElementById('userStudentId');
  
  if (userType === 'student') {
    studentIdGroup.style.display = 'block';
    studentIdInput.required = true;
  } else {
    studentIdGroup.style.display = 'none';
    studentIdInput.required = false;
    studentIdInput.value = '';
  }
};

// Modal save functions
async function saveBook(isEdit, bookId) {
  try {
    const formData = {
      title: document.getElementById('bookTitle').value.trim(),
      author: document.getElementById('bookAuthor').value.trim(),
      isbn: document.getElementById('bookISBN').value.trim(),
      published_year: parseInt(document.getElementById('bookYear').value) || null,
      category: document.getElementById('bookCategory').value,
      total_copies: parseInt(document.getElementById('bookCopies').value) || 1,
      description: document.getElementById('bookDescription').value.trim()
    };

    // Validate required fields
    if (!formData.title || !formData.author) {
      Utils.showNotification('Title and Author are required', 'error');
      return;
    }

    // Handle file upload if present
    const coverFile = document.getElementById('bookCover').files[0];
    if (coverFile) {
      try {
        const uploadResult = await FileAPI.uploadCover(coverFile);
        formData.cover_image = uploadResult.path;
      } catch (error) {
        console.error('Cover upload failed:', error);
        Utils.showNotification('Cover upload failed, but book will be saved without cover', 'warning');
      }
    }

    let result;
    if (isEdit) {
      result = await BooksAPI.update(bookId, formData);
    } else {
      result = await BooksAPI.create(formData);
    }

    Utils.showNotification(`Book ${isEdit ? 'updated' : 'added'} successfully`, 'success');
    modalManager.closeModal();

    // Refresh books list if on books section
    if (currentSection === 'books') {
      loadBooks();
    }

    // Refresh dashboard stats
    loadStatistics();

  } catch (error) {
    console.error('Error saving book:', error);
    Utils.showNotification(error.message || 'Error saving book', 'error');
  }
}

async function saveUser(isEdit, userId) {
  try {
    const formData = {
      full_name: document.getElementById('userFullName').value.trim(),
      email: document.getElementById('userEmail').value.trim(),
      phone: document.getElementById('userPhone').value.trim(),
      user_type: document.getElementById('userType').value,
      is_active: parseInt(document.getElementById('userStatus').value)
    };

    if (formData.user_type === 'student') {
      formData.student_id = document.getElementById('userStudentId').value.trim();
      if (!formData.student_id) {
        Utils.showNotification('Student ID is required for student accounts', 'error');
        return;
      }
    }

    if (!isEdit) {
      formData.password = document.getElementById('userPassword').value;
      if (!formData.password) {
        Utils.showNotification('Password is required', 'error');
        return;
      }
    }

    // Validate required fields
    if (!formData.full_name || !formData.email || !formData.user_type) {
      Utils.showNotification('Full name, email, and user type are required', 'error');
      return;
    }

    // Validate email
    if (!Utils.validateEmail(formData.email)) {
      Utils.showNotification('Please enter a valid email address', 'error');
      return;
    }

    let result;
    if (isEdit) {
      result = await UsersAPI.update(userId, formData);
    } else {
      result = await UsersAPI.create(formData);
    }

    Utils.showNotification(`User ${isEdit ? 'updated' : 'added'} successfully`, 'success');
    modalManager.closeModal();

    // Refresh users list if on users section
    if (currentSection === 'users') {
      loadUsers();
    }

    // Refresh dashboard stats
    loadStatistics();

  } catch (error) {
    console.error('Error saving user:', error);
    Utils.showNotification(error.message || 'Error saving user', 'error');
  }
}

async function saveIssue() {
  try {
    const studentInput = document.getElementById('issueStudent');
    const bookInput = document.getElementById('issueBook');

    const formData = {
      user_id: parseInt(studentInput.dataset.userId),
      book_id: parseInt(bookInput.dataset.bookId),
      due_date: document.getElementById('dueDate').value,
      notes: document.getElementById('issueNotes').value.trim()
    };

    // Validate required fields
    if (!formData.user_id || !formData.book_id || !formData.due_date) {
      Utils.showNotification('Please select a student, book, and due date', 'error');
      return;
    }

    // Validate due date is in the future
    const dueDate = new Date(formData.due_date);
    const today = new Date();
    if (dueDate <= today) {
      Utils.showNotification('Due date must be in the future', 'error');
      return;
    }

    const result = await IssuesAPI.create(formData);

    Utils.showNotification('Book issued successfully', 'success');
    modalManager.closeModal();

    // Refresh issues list if on issues section
    if (currentSection === 'issues') {
      loadIssues();
    }

    // Refresh dashboard stats
    loadStatistics();

  } catch (error) {
    console.error('Error issuing book:', error);
    Utils.showNotification(error.message || 'Error issuing book', 'error');
  }
}

async function saveProfile() {
  try {
    const formData = {
      full_name: document.getElementById('profileFullName').value.trim(),
      phone: document.getElementById('profilePhone').value.trim()
    };

    if (!formData.full_name) {
      Utils.showNotification('Full name is required', 'error');
      return;
    }

    const result = await UsersAPI.update(currentUser.id, formData);

    // Update current user info
    currentUser.full_name = formData.full_name;
    currentUser.phone = formData.phone;
    updateUserInterface();

    Utils.showNotification('Profile updated successfully', 'success');
    modalManager.closeModal();

  } catch (error) {
    console.error('Error updating profile:', error);
    Utils.showNotification(error.message || 'Error updating profile', 'error');
  }
}

async function savePassword() {
  try {
    const currentPassword = document.getElementById('currentPassword').value;
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;

    // Validate required fields
    if (!currentPassword || !newPassword || !confirmPassword) {
      Utils.showNotification('All password fields are required', 'error');
      return;
    }

    // Validate password length
    if (newPassword.length < 6) {
      Utils.showNotification('New password must be at least 6 characters long', 'error');
      return;
    }

    // Validate password confirmation
    if (newPassword !== confirmPassword) {
      Utils.showNotification('New passwords do not match', 'error');
      return;
    }

    const result = await AuthAPI.changePassword(currentPassword, newPassword);

    Utils.showNotification('Password changed successfully', 'success');
    modalManager.closeModal();

  } catch (error) {
    console.error('Error changing password:', error);
    Utils.showNotification(error.message || 'Error changing password', 'error');
  }
}

async function saveReservation() {
  try {
    const bookInput = document.getElementById('reserveBookSearch');
    const priority = document.getElementById('reservePriority').value;
    const notes = document.getElementById('reserveNotes').value.trim();

    // Validate required fields
    if (!bookInput.dataset.bookId) {
      Utils.showNotification('Please select a book', 'error');
      return;
    }

    if (!priority) {
      Utils.showNotification('Please select a priority', 'error');
      return;
    }

    const formData = {
      book_id: parseInt(bookInput.dataset.bookId),
      priority: priority,
      notes: notes
    };

    // Create reservation via API (you'll need to implement this endpoint)
    const response = await fetch('/api/reservations', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(formData)
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Failed to create reservation');
    }

    Utils.showNotification('Book reserved successfully', 'success');
    modalManager.closeModal();

    // Refresh reservations list if on reservations section
    if (currentSection === 'reservations') {
      loadReservations();
    }

  } catch (error) {
    console.error('Error creating reservation:', error);
    Utils.showNotification(error.message || 'Error creating reservation', 'error');
  }
}

// Global save functions
window.saveBook = saveBook;
window.saveUser = saveUser;
window.saveIssue = saveIssue;
window.saveProfile = saveProfile;
window.savePassword = savePassword;
window.saveReservation = saveReservation;

// Close dropdowns when clicking outside
document.addEventListener('click', (e) => {
  const dropdowns = document.querySelectorAll('.search-select-dropdown');
  dropdowns.forEach(dropdown => {
    if (!dropdown.closest('.search-select').contains(e.target)) {
      dropdown.classList.remove('show');
    }
  });
});
