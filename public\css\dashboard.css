/* Dashboard Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  color: #2c3e50;
  line-height: 1.6;
}

/* Navigation Bar */
.navbar {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  padding: 0;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.nav-container {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  height: 70px;
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.4rem;
  font-weight: 700;
  color: #3498db;
}

.nav-brand i {
  font-size: 1.8rem;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 5px;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
  background: rgba(52, 152, 219, 0.2);
  color: white;
}

.nav-link i {
  font-size: 1.1rem;
}

.nav-user {
  position: relative;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

.user-info:hover {
  background: rgba(255, 255, 255, 0.1);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: #3498db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.2rem;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-weight: 600;
  font-size: 0.9rem;
}

.user-role {
  font-size: 0.75rem;
  opacity: 0.8;
  text-transform: capitalize;
}

.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  padding: 8px 0;
  display: none;
  z-index: 1001;
}

.user-menu.show {
  display: block;
}

.user-menu a {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #2c3e50;
  text-decoration: none;
  transition: background 0.3s ease;
}

.user-menu a:hover {
  background: #f8f9fa;
}

.menu-divider {
  height: 1px;
  background: #e1e8ed;
  margin: 8px 0;
}

.nav-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  gap: 4px;
}

.nav-toggle span {
  width: 25px;
  height: 3px;
  background: white;
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* Main Content */
.main-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 30px 20px;
}

.content-section {
  display: none;
}

.content-section.active {
  display: block;
}

.section-header {
  margin-bottom: 30px;
}

.section-header h1 {
  font-size: 2.2rem;
  color: #2c3e50;
  margin-bottom: 8px;
}

.section-header p {
  color: #7f8c8d;
  font-size: 1.1rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-top: 20px;
}

.search-box {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-box i {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #7f8c8d;
}

.search-box input {
  width: 100%;
  padding: 12px 15px 12px 45px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
}

.search-box input:focus {
  outline: none;
  border-color: #3498db;
}

/* Stats Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 25px;
  margin-bottom: 40px;
}

.stat-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  color: white;
}

.stat-icon.books {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.stat-icon.users {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.stat-icon.issued {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stat-icon.overdue {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stat-info h3 {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 5px;
}

.stat-info p {
  color: #7f8c8d;
  font-size: 0.9rem;
}

/* Dashboard Content */
.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
}

.recent-activities,
.quick-actions {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.recent-activities h2,
.quick-actions h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.9rem;
}

.activity-info {
  flex: 1;
}

.activity-info h4 {
  color: #2c3e50;
  margin-bottom: 3px;
  font-size: 0.9rem;
}

.activity-info p {
  color: #7f8c8d;
  font-size: 0.8rem;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px 20px;
  background: #3498db;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.3s ease;
}

.btn-primary {
  background: #3498db;
  color: white;
}

.btn-primary:hover {
  background: #2980b9;
  transform: translateY(-1px);
}

.btn-secondary {
  background: #95a5a6;
  color: white;
}

.btn-secondary:hover {
  background: #7f8c8d;
}

.btn-success {
  background: #27ae60;
  color: white;
}

.btn-success:hover {
  background: #229954;
}

.btn-warning {
  background: #f39c12;
  color: white;
}

.btn-warning:hover {
  background: #e67e22;
}

.btn-danger {
  background: #e74c3c;
  color: white;
}

.btn-danger:hover {
  background: #c0392b;
}

.btn-sm {
  padding: 8px 12px;
  font-size: 0.8rem;
}

/* Tables */
.table-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table th {
  background: #f8f9fa;
  padding: 15px;
  text-align: left;
  font-weight: 600;
  color: #2c3e50;
  border-bottom: 2px solid #e1e8ed;
}

.data-table td {
  padding: 15px;
  border-bottom: 1px solid #e1e8ed;
  vertical-align: middle;
}

.data-table tr:hover {
  background: #f8f9fa;
}

.book-cover {
  width: 40px;
  height: 50px;
  object-fit: cover;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-available {
  background: #d5f4e6;
  color: #27ae60;
}

.status-issued {
  background: #fef9e7;
  color: #f39c12;
}

.status-overdue {
  background: #fadbd8;
  color: #e74c3c;
}

.status-active {
  background: #d6eaf8;
  color: #3498db;
}

/* Filter Tabs */
.filter-tabs {
  display: flex;
  gap: 5px;
  background: #f8f9fa;
  padding: 5px;
  border-radius: 8px;
}

.filter-tab {
  padding: 8px 16px;
  background: none;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  color: #7f8c8d;
  transition: all 0.3s ease;
}

.filter-tab.active,
.filter-tab:hover {
  background: white;
  color: #2c3e50;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Reports */
.reports-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
}

.report-card {
  background: white;
  padding: 25px;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.report-card h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.2rem;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  background: white;
  padding: 30px;
  border-radius: 15px;
  text-align: center;
  color: #2c3e50;
}

.loading-spinner i {
  font-size: 2rem;
  color: #3498db;
  margin-bottom: 15px;
}

/* Utility Classes */
.admin-only {
  display: none;
}

body.admin .admin-only {
  display: block;
}

body.admin .nav-link.admin-only {
  display: flex;
}

body.admin .stat-card.admin-only {
  display: flex;
}

body.admin .content-section.admin-only {
  display: none;
}

body.admin .content-section.admin-only.active {
  display: block;
}

.text-center {
  text-align: center;
}

.mt-20 {
  margin-top: 20px;
}

.mb-20 {
  margin-bottom: 20px;
}

/* Notifications */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  padding: 15px 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  z-index: 10001;
  min-width: 300px;
  animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.notification-success {
  border-left: 4px solid #27ae60;
}

.notification-error {
  border-left: 4px solid #e74c3c;
}

.notification-warning {
  border-left: 4px solid #f39c12;
}

.notification-info {
  border-left: 4px solid #3498db;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
}

.notification-success .notification-content i {
  color: #27ae60;
}

.notification-error .notification-content i {
  color: #e74c3c;
}

.notification-warning .notification-content i {
  color: #f39c12;
}

.notification-info .notification-content i {
  color: #3498db;
}

.notification-close {
  background: none;
  border: none;
  color: #7f8c8d;
  cursor: pointer;
  padding: 5px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.notification-close:hover {
  background: #f8f9fa;
  color: #2c3e50;
}

/* Report Styles */
.stat-item {
  padding: 10px 0;
  border-bottom: 1px solid #e1e8ed;
  display: flex;
  justify-content: space-between;
}

.stat-item:last-child {
  border-bottom: none;
}

.overdue-list,
.popular-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.overdue-item,
.popular-item {
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #e74c3c;
}

.popular-item {
  border-left-color: #3498db;
  display: flex;
  align-items: flex-start;
  gap: 10px;
}

.rank {
  font-weight: bold;
  color: #3498db;
  min-width: 20px;
}

/* Confirm Modal */
.confirm-modal .modal-content {
  max-width: 450px;
}

.confirm-modal .modal-header i {
  color: #f39c12;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-menu {
    display: none;
  }

  .nav-toggle {
    display: flex;
  }

  .dashboard-content {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .search-box {
    max-width: none;
  }

  .reports-grid {
    grid-template-columns: 1fr;
  }

  .filter-tabs {
    flex-wrap: wrap;
  }

  .user-details {
    display: none;
  }

  .notification {
    right: 10px;
    left: 10px;
    min-width: auto;
  }

  /* Mobile Navigation Enhancements */
  .nav-container {
    padding: 0 15px;
    height: 60px;
  }

  .nav-brand {
    font-size: 1.2rem;
  }

  .nav-menu {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    bottom: 0;
    background: #2c3e50;
    flex-direction: column;
    padding: 20px;
    gap: 10px;
    overflow-y: auto;
    z-index: 1000;
  }

  .nav-menu.show {
    display: flex;
  }

  .nav-link {
    padding: 15px 20px;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    justify-content: flex-start;
  }

  .nav-user {
    order: -1;
  }

  .user-info {
    padding: 15px 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 20px;
  }

  .user-details {
    display: flex;
  }

  .main-content {
    padding: 20px 15px;
  }

  .stats-grid {
    gap: 15px;
  }

  .stat-card {
    padding: 20px;
  }

  .dashboard-content {
    gap: 20px;
  }

  .section-header h1 {
    font-size: 1.8rem;
  }

  .data-table {
    font-size: 0.9rem;
  }

  .data-table th,
  .data-table td {
    padding: 10px 8px;
  }

  .btn {
    padding: 10px 16px;
    font-size: 0.85rem;
  }
}

/* Mobile Navigation Overlay */
.mobile-nav-overlay {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.mobile-nav-overlay.show {
  display: block;
}

/* Enhanced Navigation Submenus */
.nav-submenu {
  position: absolute;
  top: 100%;
  left: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  padding: 8px 0;
  display: none;
  z-index: 1001;
}

.nav-submenu.show {
  display: block;
}

.nav-submenu a {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  color: #2c3e50;
  text-decoration: none;
  transition: background 0.3s ease;
}

.nav-submenu a:hover {
  background: #f8f9fa;
}

/* Catalog Grid */
.catalog-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 25px;
  margin-top: 20px;
}

.catalog-item {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.catalog-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.catalog-cover {
  height: 200px;
  overflow: hidden;
  position: relative;
}

.catalog-cover img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.placeholder-cover {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #e1e8ed, #f8f9fa);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 3rem;
  color: #7f8c8d;
}

.catalog-info {
  padding: 20px;
}

.catalog-info h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
  line-height: 1.3;
}

.catalog-info .author {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-bottom: 8px;
}

.catalog-info .category {
  color: #3498db;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 12px;
}

.availability {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 0.9rem;
}

.available-count {
  color: #27ae60;
  font-weight: 600;
}

.total-count {
  color: #7f8c8d;
}

/* Priority and Status Badges */
.priority-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.priority-1, .priority-low {
  background: #d5f4e6;
  color: #27ae60;
}

.priority-2, .priority-medium {
  background: #fff3cd;
  color: #f39c12;
}

.priority-3, .priority-high {
  background: #f8d7da;
  color: #e74c3c;
}

.priority-urgent {
  background: #721c24;
  color: white;
}

/* Filter Select */
.filter-select {
  padding: 10px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 0.9rem;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.filter-select:focus {
  outline: none;
  border-color: #3498db;
}

/* Extra Small Devices */
@media (max-width: 480px) {
  .nav-container {
    padding: 0 10px;
  }

  .main-content {
    padding: 15px 10px;
  }

  .stat-card {
    flex-direction: column;
    text-align: center;
    padding: 15px;
  }

  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 1.3rem;
  }

  .section-header h1 {
    font-size: 1.5rem;
  }

  .data-table th,
  .data-table td {
    padding: 8px 5px;
    font-size: 0.8rem;
  }

  .btn {
    padding: 8px 12px;
    font-size: 0.8rem;
  }

  .modal-content {
    margin: 10px;
    max-height: calc(100vh - 20px);
  }

  .catalog-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .catalog-item {
    margin: 0 5px;
  }

  .catalog-cover {
    height: 150px;
  }

  .catalog-info {
    padding: 15px;
  }
}

/* Days Issued Column Styling */
.days-issued-ongoing {
  color: #3498db;
  font-weight: bold;
}

.days-issued-completed {
  color: #27ae60;
  font-weight: bold;
}

.days-issued-overdue {
  color: #e74c3c;
  font-weight: bold;
}

/* Overdue Row Styling */
.overdue-row {
  background-color: #fdf2f2 !important;
  border-left: 4px solid #e74c3c;
}

.overdue-row:hover {
  background-color: #fbeaea !important;
}

/* Center align the Days Issued column */
.data-table td:nth-child(6) {
  text-align: center;
  vertical-align: middle;
}

.data-table th:nth-child(6) {
  text-align: center;
}

/* Enhanced table styling for better readability */
.data-table td {
  line-height: 1.4;
}

.data-table td small {
  display: block;
  margin-top: 2px;
  font-size: 0.75rem;
  opacity: 0.8;
}
