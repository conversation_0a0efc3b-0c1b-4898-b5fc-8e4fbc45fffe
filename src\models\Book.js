const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./library.db');

// Create books table with all columns
db.serialize(() => {
  db.run(`
    CREATE TABLE IF NOT EXISTS books (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      author TEXT NOT NULL,
      isbn TEXT UNIQUE,
      published_year INTEGER,
      category TEXT,
      total_copies INTEGER DEFAULT 1,
      available_copies INTEGER DEFAULT 1,
      cover_image TEXT,
      description TEXT,
      status TEXT DEFAULT 'available',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Check if we need to migrate existing data
  db.all("PRAGMA table_info(books)", (err, columns) => {
    if (!err) {
      const columnNames = columns.map(col => col.name);

      // If old schema detected, migrate data
      if (!columnNames.includes('category')) {
        console.log('Migrating books table to new schema...');

        // Create new table with correct schema
        db.run(`
          CREATE TABLE books_new (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            title TEXT NOT NULL,
            author TEXT NOT NULL,
            isbn TEXT UNIQUE,
            published_year INTEGER,
            category TEXT,
            total_copies INTEGER DEFAULT 1,
            available_copies INTEGER DEFAULT 1,
            cover_image TEXT,
            description TEXT,
            status TEXT DEFAULT 'available',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
          )
        `);

        // Copy existing data
        db.run(`
          INSERT INTO books_new (id, title, author, isbn, published_year, status, total_copies, available_copies)
          SELECT id, title, author, isbn, published_year, status, 1, 1 FROM books
        `);

        // Replace old table
        db.run('DROP TABLE books');
        db.run('ALTER TABLE books_new RENAME TO books');

        console.log('Books table migration completed.');
      }
    }
  });
});

// Add some sample books if the table is empty
db.serialize(() => {
  setTimeout(() => {
    db.get('SELECT COUNT(*) as count FROM books', (err, result) => {
      if (!err && result.count === 0) {
        console.log('Adding sample books...');
        const sampleBooks = [
          {
            title: 'The Great Gatsby',
            author: 'F. Scott Fitzgerald',
            isbn: '978-0-7432-7356-5',
            published_year: 1925,
            category: 'Fiction',
            total_copies: 3,
            available_copies: 3,
            description: 'A classic American novel set in the Jazz Age.'
          },
          {
            title: 'To Kill a Mockingbird',
            author: 'Harper Lee',
            isbn: '978-0-06-112008-4',
            published_year: 1960,
            category: 'Fiction',
            total_copies: 2,
            available_copies: 2,
            description: 'A gripping tale of racial injustice and childhood innocence.'
          },
          {
            title: 'Introduction to Algorithms',
            author: 'Thomas H. Cormen',
            isbn: '978-0-262-03384-8',
            published_year: 2009,
            category: 'Technology',
            total_copies: 5,
            available_copies: 5,
            description: 'Comprehensive introduction to algorithms and data structures.'
          },
          {
            title: 'A Brief History of Time',
            author: 'Stephen Hawking',
            isbn: '978-0-553-38016-3',
            published_year: 1988,
            category: 'Science',
            total_copies: 2,
            available_copies: 2,
            description: 'A landmark volume in science writing by one of the great minds of our time.'
          },
          {
            title: 'The Art of War',
            author: 'Sun Tzu',
            isbn: '978-1-59030-963-7',
            published_year: -500,
            category: 'History',
            total_copies: 1,
            available_copies: 1,
            description: 'Ancient Chinese military treatise on strategy and tactics.'
          }
        ];

        sampleBooks.forEach((book, index) => {
          db.run(
            'INSERT INTO books (title, author, isbn, published_year, category, total_copies, available_copies, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
            [book.title, book.author, book.isbn, book.published_year, book.category, book.total_copies, book.available_copies, book.description],
            (err) => {
              if (err) {
                console.error('Error inserting sample book:', err.message);
              } else if (index === sampleBooks.length - 1) {
                console.log('Sample books added successfully.');
              }
            }
          );
        });
      }
    });
  }, 1000); // Wait 1 second to ensure table is ready
});

class Book {
  static getAll(callback) {
    db.all('SELECT * FROM books', callback);
  }

  static getById(id, callback) {
    db.get('SELECT * FROM books WHERE id = ?', [id], callback);
  }

  static create(book, callback) {
    const { title, author, isbn, published_year, category, total_copies, cover_image, description } = book;
    const availableCopies = total_copies || 1;

    db.run(
      'INSERT INTO books (title, author, isbn, published_year, category, total_copies, available_copies, cover_image, description) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
      [title, author, isbn, published_year, category, total_copies || 1, availableCopies, cover_image, description],
      function(err) {
        if (err) {
          callback(err);
        } else {
          callback(null, { id: this.lastID, ...book, available_copies: availableCopies });
        }
      }
    );
  }

  static update(id, book, callback) {
    const { title, author, isbn, published_year, category, total_copies, cover_image, description, status } = book;

    // If total_copies is being updated, adjust available_copies proportionally
    db.get('SELECT total_copies, available_copies FROM books WHERE id = ?', [id], (err, currentBook) => {
      if (err) return callback(err);

      let newAvailableCopies = book.available_copies;
      if (total_copies && currentBook) {
        const difference = total_copies - currentBook.total_copies;
        newAvailableCopies = Math.max(0, currentBook.available_copies + difference);
      }

      db.run(
        'UPDATE books SET title = ?, author = ?, isbn = ?, published_year = ?, category = ?, total_copies = ?, available_copies = ?, cover_image = ?, description = ?, status = ? WHERE id = ?',
        [title, author, isbn, published_year, category, total_copies, newAvailableCopies, cover_image, description, status, id],
        callback
      );
    });
  }

  static delete(id, callback) {
    // Check if book has any active issues
    db.get('SELECT COUNT(*) as count FROM issues WHERE book_id = ? AND status = "issued"', [id], (err, result) => {
      if (err) return callback(err);
      if (result.count > 0) {
        return callback(new Error('Cannot delete book with active issues'));
      }

      db.run('DELETE FROM books WHERE id = ?', [id], callback);
    });
  }

  static getAvailableBooks(callback) {
    db.all('SELECT * FROM books WHERE available_copies > 0 AND status = "available"', callback);
  }

  static searchBooks(searchTerm, callback) {
    const term = `%${searchTerm}%`;
    db.all(
      'SELECT * FROM books WHERE title LIKE ? OR author LIKE ? OR isbn LIKE ? OR category LIKE ?',
      [term, term, term, term],
      callback
    );
  }

  static getBooksByCategory(category, callback) {
    db.all('SELECT * FROM books WHERE category = ?', [category], callback);
  }

  static getCategories(callback) {
    db.all('SELECT DISTINCT category FROM books WHERE category IS NOT NULL ORDER BY category', callback);
  }

  static getStatistics(callback) {
    const queries = {
      totalBooks: 'SELECT COUNT(*) as count FROM books',
      totalCopies: 'SELECT SUM(total_copies) as total FROM books',
      availableCopies: 'SELECT SUM(available_copies) as total FROM books',
      issuedCopies: 'SELECT SUM(total_copies - available_copies) as total FROM books'
    };

    const stats = {};
    let completed = 0;
    const total = Object.keys(queries).length;

    Object.entries(queries).forEach(([key, query]) => {
      db.get(query, (err, result) => {
        if (!err) {
          stats[key] = result.count || result.total || 0;
        }
        completed++;
        if (completed === total) {
          callback(null, stats);
        }
      });
    });
  }
}

module.exports = Book;