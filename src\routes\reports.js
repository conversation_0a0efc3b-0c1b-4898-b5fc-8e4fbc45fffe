const express = require('express');
const router = express.Router();
const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./library.db');

// Middleware to check if user is authenticated
const requireAuth = (req, res, next) => {
  if (!req.session.user) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (!req.session.user || req.session.user.user_type !== 'library_admin') {
    return res.status(403).json({ error: 'Admin access required' });
  }
  next();
};

// User Reports
router.get('/users', requireAuth, requireAdmin, (req, res) => {
  const query = `
    SELECT 
      u.id,
      u.full_name,
      u.email,
      u.phone,
      u.user_type,
      u.student_id,
      u.is_active,
      u.is_blocked,
      u.created_at,
      COUNT(DISTINCT i.id) as total_books_issued,
      COUNT(DISTINCT CASE WHEN i.status = 'issued' THEN i.id END) as currently_issued,
      COUNT(DISTINCT CASE WHEN i.status = 'returned' THEN i.id END) as books_returned,
      COUNT(DISTINCT CASE WHEN i.due_date < datetime('now') AND i.status = 'issued' THEN i.id END) as overdue_books,
      COALESCE(SUM(i.fine_amount), 0) as total_fines,
      COALESCE(SUM(CASE WHEN COALESCE(i.fine_paid, 0) = 0 THEN i.fine_amount ELSE 0 END), 0) as unpaid_fines
    FROM users u
    LEFT JOIN issues i ON u.id = i.user_id
    GROUP BY u.id, u.full_name, u.email, u.phone, u.user_type, u.student_id, u.is_active, u.is_blocked, u.created_at
    ORDER BY u.created_at DESC
  `;

  db.all(query, (err, users) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(users);
  });
});

// Book Reports
router.get('/books', requireAuth, requireAdmin, (req, res) => {
  const query = `
    SELECT 
      b.id,
      b.title,
      b.author,
      b.isbn,
      b.published_year,
      b.category,
      b.total_copies,
      b.available_copies,
      b.status,
      b.created_at,
      COUNT(DISTINCT i.id) as total_times_issued,
      COUNT(DISTINCT CASE WHEN i.status = 'issued' THEN i.id END) as currently_issued,
      COUNT(DISTINCT CASE WHEN i.status = 'returned' THEN i.id END) as times_returned,
      COUNT(DISTINCT CASE WHEN i.due_date < datetime('now') AND i.status = 'issued' THEN i.id END) as overdue_count,
      AVG(CASE WHEN i.return_date IS NOT NULL 
          THEN julianday(i.return_date) - julianday(i.issue_date) 
          END) as avg_issue_duration,
      MAX(i.issue_date) as last_issued_date
    FROM books b
    LEFT JOIN issues i ON b.id = i.book_id
    GROUP BY b.id, b.title, b.author, b.isbn, b.published_year, b.category, 
             b.total_copies, b.available_copies, b.status, b.created_at
    ORDER BY total_times_issued DESC, b.title
  `;

  db.all(query, (err, books) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(books);
  });
});

// Book Issue Reports
router.get('/issues', requireAuth, requireAdmin, (req, res) => {
  const { start_date, end_date, status, user_id, book_id } = req.query;
  
  let query = `
    SELECT 
      i.id,
      i.issue_date,
      i.due_date,
      i.return_date,
      i.status,
      i.fine_amount,
      i.fine_paid,
      i.notes,
      b.title as book_title,
      b.author as book_author,
      b.isbn as book_isbn,
      b.category as book_category,
      u.full_name as user_name,
      u.email as user_email,
      u.student_id,
      u.user_type,
      CASE 
        WHEN i.return_date IS NOT NULL 
        THEN julianday(i.return_date) - julianday(i.issue_date)
        ELSE julianday('now') - julianday(i.issue_date)
      END as days_issued,
      CASE 
        WHEN i.status = 'issued' AND i.due_date < datetime('now')
        THEN julianday('now') - julianday(i.due_date)
        ELSE 0
      END as days_overdue
    FROM issues i
    JOIN books b ON i.book_id = b.id
    JOIN users u ON i.user_id = u.id
    WHERE 1=1
  `;

  const params = [];

  if (start_date) {
    query += ' AND date(i.issue_date) >= date(?)';
    params.push(start_date);
  }

  if (end_date) {
    query += ' AND date(i.issue_date) <= date(?)';
    params.push(end_date);
  }

  if (status) {
    query += ' AND i.status = ?';
    params.push(status);
  }

  if (user_id) {
    query += ' AND i.user_id = ?';
    params.push(user_id);
  }

  if (book_id) {
    query += ' AND i.book_id = ?';
    params.push(book_id);
  }

  query += ' ORDER BY i.issue_date DESC';

  db.all(query, params, (err, issues) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(issues);
  });
});

// Summary Statistics
router.get('/summary', requireAuth, requireAdmin, (req, res) => {
  const queries = {
    userStats: `
      SELECT 
        COUNT(*) as total_users,
        COUNT(CASE WHEN user_type = 'student' THEN 1 END) as total_students,
        COUNT(CASE WHEN user_type = 'library_admin' THEN 1 END) as total_admins,
        COUNT(CASE WHEN is_active = 1 AND is_blocked = 0 THEN 1 END) as active_users,
        COUNT(CASE WHEN is_blocked = 1 THEN 1 END) as blocked_users,
        COUNT(CASE WHEN is_active = 0 THEN 1 END) as inactive_users
      FROM users
    `,
    bookStats: `
      SELECT 
        COUNT(*) as total_books,
        SUM(total_copies) as total_copies,
        SUM(available_copies) as available_copies,
        SUM(total_copies - available_copies) as issued_copies,
        COUNT(DISTINCT category) as total_categories
      FROM books
    `,
    issueStats: `
      SELECT 
        COUNT(*) as total_issues,
        COUNT(CASE WHEN status = 'issued' THEN 1 END) as active_issues,
        COUNT(CASE WHEN status = 'returned' THEN 1 END) as returned_issues,
        COUNT(CASE WHEN status = 'issued' AND due_date < datetime('now') THEN 1 END) as overdue_issues,
        SUM(fine_amount) as total_fines,
        SUM(CASE WHEN COALESCE(fine_paid, 0) = 0 THEN fine_amount ELSE 0 END) as unpaid_fines,
        AVG(CASE WHEN return_date IS NOT NULL 
            THEN julianday(return_date) - julianday(issue_date) 
            END) as avg_issue_duration
      FROM issues
    `
  };

  const results = {};
  let completed = 0;
  const totalQueries = Object.keys(queries).length;

  Object.entries(queries).forEach(([key, query]) => {
    db.get(query, (err, result) => {
      if (err) {
        return res.status(500).json({ error: err.message });
      }
      
      results[key] = result;
      completed++;
      
      if (completed === totalQueries) {
        res.json(results);
      }
    });
  });
});

// Popular Books Report
router.get('/popular-books', requireAuth, requireAdmin, (req, res) => {
  const query = `
    SELECT 
      b.id,
      b.title,
      b.author,
      b.category,
      COUNT(i.id) as issue_count,
      COUNT(DISTINCT i.user_id) as unique_users,
      AVG(CASE WHEN i.return_date IS NOT NULL 
          THEN julianday(i.return_date) - julianday(i.issue_date) 
          END) as avg_duration,
      MAX(i.issue_date) as last_issued
    FROM books b
    LEFT JOIN issues i ON b.id = i.book_id
    GROUP BY b.id, b.title, b.author, b.category
    HAVING issue_count > 0
    ORDER BY issue_count DESC, unique_users DESC
    LIMIT 20
  `;

  db.all(query, (err, books) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(books);
  });
});

// Overdue Report
router.get('/overdue', requireAuth, requireAdmin, (req, res) => {
  const query = `
    SELECT 
      i.id,
      i.issue_date,
      i.due_date,
      julianday('now') - julianday(i.due_date) as days_overdue,
      i.fine_amount,
      i.fine_paid,
      b.title as book_title,
      b.author as book_author,
      u.full_name as user_name,
      u.email as user_email,
      u.phone as user_phone,
      u.student_id
    FROM issues i
    JOIN books b ON i.book_id = b.id
    JOIN users u ON i.user_id = u.id
    WHERE i.status = 'issued' AND i.due_date < datetime('now')
    ORDER BY days_overdue DESC
  `;

  db.all(query, (err, overdueIssues) => {
    if (err) {
      return res.status(500).json({ error: err.message });
    }
    res.json(overdueIssues);
  });
});

module.exports = router;
