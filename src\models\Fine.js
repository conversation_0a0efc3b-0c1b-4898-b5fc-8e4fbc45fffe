const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./library.db');

// Create fines table
db.run(`
  CREATE TABLE IF NOT EXISTS fines (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    issue_id INTEGER,
    book_id INTEGER,
    amount DECIMAL(10,2) NOT NULL,
    reason TEXT NOT NULL,
    status TEXT DEFAULT 'pending' CHECK(status IN ('pending', 'paid', 'waived', 'partial')),
    paid_amount DECIMAL(10,2) DEFAULT 0,
    issued_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    paid_date DATETIME,
    waived_date DATETIME,
    waived_by INTEGER,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users (id),
    FOREIG<PERSON> KEY (issue_id) REFERENCES issues (id),
    FOREIG<PERSON> KEY (book_id) REFERENCES books (id),
    <PERSON>OR<PERSON><PERSON><PERSON> KEY (waived_by) REFERENCES users (id)
  )
`);

class Fine {
  static getAll(callback) {
    const query = `
      SELECT f.*, 
             u.full_name as user_name, u.email as user_email, u.student_id,
             b.title as book_title, b.author as book_author,
             wb.full_name as waived_by_name
      FROM fines f
      JOIN users u ON f.user_id = u.id
      LEFT JOIN books b ON f.book_id = b.id
      LEFT JOIN users wb ON f.waived_by = wb.id
      ORDER BY f.issued_date DESC
    `;
    
    db.all(query, callback);
  }

  static getById(id, callback) {
    const query = `
      SELECT f.*, 
             u.full_name as user_name, u.email as user_email, u.student_id,
             b.title as book_title, b.author as book_author,
             wb.full_name as waived_by_name
      FROM fines f
      JOIN users u ON f.user_id = u.id
      LEFT JOIN books b ON f.book_id = b.id
      LEFT JOIN users wb ON f.waived_by = wb.id
      WHERE f.id = ?
    `;
    
    db.get(query, [id], callback);
  }

  static getByUser(userId, callback) {
    const query = `
      SELECT f.*, 
             b.title as book_title, b.author as book_author
      FROM fines f
      LEFT JOIN books b ON f.book_id = b.id
      WHERE f.user_id = ?
      ORDER BY f.issued_date DESC
    `;
    
    db.all(query, [userId], callback);
  }

  static create(fine, callback) {
    const { user_id, issue_id, book_id, amount, reason, notes } = fine;
    
    db.run(
      'INSERT INTO fines (user_id, issue_id, book_id, amount, reason, notes) VALUES (?, ?, ?, ?, ?, ?)',
      [user_id, issue_id, book_id, amount, reason, notes],
      function(err) {
        if (err) return callback(err);
        callback(null, { id: this.lastID, ...fine });
      }
    );
  }

  static update(id, fine, callback) {
    const { amount, reason, status, notes } = fine;
    
    db.run(
      'UPDATE fines SET amount = ?, reason = ?, status = ?, notes = ? WHERE id = ?',
      [amount, reason, status, notes, id],
      callback
    );
  }

  static payFine(id, paidAmount, callback) {
    // Get current fine details
    db.get('SELECT amount, paid_amount FROM fines WHERE id = ?', [id], (err, fine) => {
      if (err) return callback(err);
      if (!fine) return callback(new Error('Fine not found'));
      
      const newPaidAmount = (fine.paid_amount || 0) + paidAmount;
      const status = newPaidAmount >= fine.amount ? 'paid' : 'partial';
      const paidDate = status === 'paid' ? new Date().toISOString() : null;
      
      db.run(
        'UPDATE fines SET paid_amount = ?, status = ?, paid_date = ? WHERE id = ?',
        [newPaidAmount, status, paidDate, id],
        callback
      );
    });
  }

  static waiveFine(id, waivedBy, callback) {
    const waivedDate = new Date().toISOString();
    
    db.run(
      'UPDATE fines SET status = "waived", waived_date = ?, waived_by = ? WHERE id = ?',
      [waivedDate, waivedBy, id],
      callback
    );
  }

  static delete(id, callback) {
    db.run('DELETE FROM fines WHERE id = ?', [id], callback);
  }

  static getStats(callback) {
    const query = `
      SELECT 
        COUNT(*) as total_fines,
        SUM(amount) as total_amount,
        SUM(paid_amount) as total_paid,
        COUNT(CASE WHEN status = 'pending' THEN 1 END) as pending_count,
        COUNT(CASE WHEN status = 'paid' THEN 1 END) as paid_count,
        COUNT(CASE WHEN status = 'waived' THEN 1 END) as waived_count,
        COUNT(CASE WHEN status = 'partial' THEN 1 END) as partial_count,
        SUM(CASE WHEN status = 'pending' THEN amount END) as pending_amount,
        SUM(CASE WHEN status = 'partial' THEN (amount - paid_amount) END) as outstanding_amount
      FROM fines
    `;
    
    db.get(query, callback);
  }

  static getUserFinesSummary(userId, callback) {
    const query = `
      SELECT 
        COUNT(*) as total_fines,
        SUM(amount) as total_amount,
        SUM(paid_amount) as total_paid,
        SUM(CASE WHEN status IN ('pending', 'partial') THEN (amount - COALESCE(paid_amount, 0)) END) as outstanding_amount
      FROM fines
      WHERE user_id = ?
    `;
    
    db.get(query, [userId], callback);
  }

  // Auto-generate overdue fines
  static generateOverdueFines(callback) {
    const query = `
      SELECT i.id, i.user_id, i.book_id, i.due_date,
             JULIANDAY('now') - JULIANDAY(i.due_date) as days_overdue
      FROM issues i
      LEFT JOIN fines f ON i.id = f.issue_id AND f.reason = 'Overdue'
      WHERE i.return_date IS NULL 
        AND i.due_date < datetime('now')
        AND f.id IS NULL
    `;
    
    db.all(query, (err, overdueIssues) => {
      if (err) return callback(err);
      
      let processed = 0;
      const total = overdueIssues.length;
      
      if (total === 0) return callback(null, { generated: 0 });
      
      overdueIssues.forEach(issue => {
        const fineAmount = Math.min(issue.days_overdue * 0.50, 25.00); // $0.50 per day, max $25
        
        db.run(
          'INSERT INTO fines (user_id, issue_id, book_id, amount, reason) VALUES (?, ?, ?, ?, ?)',
          [issue.user_id, issue.id, issue.book_id, fineAmount, 'Overdue'],
          (err) => {
            processed++;
            if (processed === total) {
              callback(err, { generated: err ? 0 : total });
            }
          }
        );
      });
    });
  }
}

module.exports = Fine;
