const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./library.db');

// Create issues table if it doesn't exist
db.run(`
  CREATE TABLE IF NOT EXISTS issues (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    issue_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    due_date DATETIME NOT NULL,
    return_date DATETIME,
    status TEXT DEFAULT 'issued' CHECK(status IN ('issued', 'returned', 'overdue')),
    fine_amount DECIMAL(10,2) DEFAULT 0,
    fine_paid INTEGER DEFAULT 0,
    notes TEXT,
    FOREIGN KEY (book_id) REFERENCES books (id),
    FOREIGN KEY (user_id) REFERENCES users (id)
  )
`);

// Add fine_paid column if it doesn't exist (for existing databases)
db.run(`ALTER TABLE issues ADD COLUMN fine_paid INTEGER DEFAULT 0`, (err) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.error('Error adding fine_paid column:', err.message);
  }
});

class Issue {
  static getAll(callback) {
    const query = `
      SELECT 
        i.*,
        b.title as book_title,
        b.author as book_author,
        b.isbn as book_isbn,
        u.full_name as user_name,
        u.email as user_email,
        u.student_id
      FROM issues i
      JOIN books b ON i.book_id = b.id
      JOIN users u ON i.user_id = u.id
      ORDER BY i.issue_date DESC
    `;
    db.all(query, callback);
  }

  static getById(id, callback) {
    const query = `
      SELECT 
        i.*,
        b.title as book_title,
        b.author as book_author,
        b.isbn as book_isbn,
        u.full_name as user_name,
        u.email as user_email,
        u.student_id
      FROM issues i
      JOIN books b ON i.book_id = b.id
      JOIN users u ON i.user_id = u.id
      WHERE i.id = ?
    `;
    db.get(query, [id], callback);
  }

  static getByUserId(userId, callback) {
    const query = `
      SELECT 
        i.*,
        b.title as book_title,
        b.author as book_author,
        b.isbn as book_isbn
      FROM issues i
      JOIN books b ON i.book_id = b.id
      WHERE i.user_id = ?
      ORDER BY i.issue_date DESC
    `;
    db.all(query, [userId], callback);
  }

  static getActiveIssues(callback) {
    const query = `
      SELECT 
        i.*,
        b.title as book_title,
        b.author as book_author,
        u.full_name as user_name,
        u.student_id
      FROM issues i
      JOIN books b ON i.book_id = b.id
      JOIN users u ON i.user_id = u.id
      WHERE i.status = 'issued'
      ORDER BY i.due_date ASC
    `;
    db.all(query, callback);
  }

  static getOverdueIssues(callback) {
    const query = `
      SELECT 
        i.*,
        b.title as book_title,
        b.author as book_author,
        u.full_name as user_name,
        u.student_id,
        u.email as user_email
      FROM issues i
      JOIN books b ON i.book_id = b.id
      JOIN users u ON i.user_id = u.id
      WHERE i.status = 'issued' AND i.due_date < datetime('now')
      ORDER BY i.due_date ASC
    `;
    db.all(query, callback);
  }

  static create(issue, callback) {
    const { book_id, user_id, due_date, notes } = issue;
    
    // First check if book is available
    db.get('SELECT available_copies FROM books WHERE id = ?', [book_id], (err, book) => {
      if (err) return callback(err);
      if (!book || book.available_copies <= 0) {
        return callback(new Error('Book is not available for issue'));
      }
      
      // Create the issue
      db.run(
        'INSERT INTO issues (book_id, user_id, due_date, notes) VALUES (?, ?, ?, ?)',
        [book_id, user_id, due_date, notes],
        function(err) {
          if (err) return callback(err);
          
          // Update book available copies
          db.run(
            'UPDATE books SET available_copies = available_copies - 1 WHERE id = ?',
            [book_id],
            (updateErr) => {
              if (updateErr) return callback(updateErr);
              callback(null, { id: this.lastID, ...issue });
            }
          );
        }
      );
    });
  }

  static returnBook(id, callback) {
    // Get issue details first
    db.get('SELECT * FROM issues WHERE id = ?', [id], (err, issue) => {
      if (err) return callback(err);
      if (!issue) return callback(new Error('Issue not found'));
      
      // Update issue status
      db.run(
        'UPDATE issues SET status = "returned", return_date = datetime("now") WHERE id = ?',
        [id],
        (updateErr) => {
          if (updateErr) return callback(updateErr);
          
          // Update book available copies
          db.run(
            'UPDATE books SET available_copies = available_copies + 1 WHERE id = ?',
            [issue.book_id],
            callback
          );
        }
      );
    });
  }

  static updateFine(id, fineAmount, callback) {
    db.run('UPDATE issues SET fine_amount = ? WHERE id = ?', [fineAmount, id], callback);
  }

  static payFine(id, callback) {
    db.run('UPDATE issues SET fine_paid = 1 WHERE id = ?', [id], callback);
  }

  static waiveFine(id, callback) {
    db.run('UPDATE issues SET fine_amount = 0, fine_paid = 1 WHERE id = ?', [id], callback);
  }

  static delete(id, callback) {
    db.run('DELETE FROM issues WHERE id = ?', [id], callback);
  }

  static getStatistics(callback) {
    const queries = {
      totalIssued: 'SELECT COUNT(*) as count FROM issues WHERE status = "issued"',
      totalReturned: 'SELECT COUNT(*) as count FROM issues WHERE status = "returned"',
      totalOverdue: 'SELECT COUNT(*) as count FROM issues WHERE status = "issued" AND due_date < datetime("now")',
      totalFines: 'SELECT SUM(fine_amount) as total FROM issues WHERE fine_amount > 0'
    };

    const stats = {};
    let completed = 0;
    const total = Object.keys(queries).length;

    Object.entries(queries).forEach(([key, query]) => {
      db.get(query, (err, result) => {
        if (!err) {
          stats[key] = result.count || result.total || 0;
        }
        completed++;
        if (completed === total) {
          callback(null, stats);
        }
      });
    });
  }
}

module.exports = Issue;
