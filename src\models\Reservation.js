const sqlite3 = require('sqlite3').verbose();
const db = new sqlite3.Database('./library.db');

// Create reservations table
db.run(`
  CREATE TABLE IF NOT EXISTS reservations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    book_id INTEGER NOT NULL,
    user_id INTEGER NOT NULL,
    reserved_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    status TEXT DEFAULT 'active' CHECK(status IN ('active', 'fulfilled', 'cancelled', 'expired')),
    priority INTEGER DEFAULT 1,
    notes TEXT,
    expires_at DATETIME,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (book_id) REFERENCES books (id),
    FOREIGN KEY (user_id) REFERENCES users (id)
  )
`);

class Reservation {
  static getAll(callback) {
    const query = `
      SELECT r.*, 
             b.title as book_title, b.author as book_author, b.isbn,
             u.full_name as user_name, u.email as user_email, u.student_id
      FROM reservations r
      JOIN books b ON r.book_id = b.id
      JOIN users u ON r.user_id = u.id
      ORDER BY r.priority DESC, r.reserved_date ASC
    `;
    
    db.all(query, callback);
  }

  static getById(id, callback) {
    const query = `
      SELECT r.*, 
             b.title as book_title, b.author as book_author, b.isbn,
             u.full_name as user_name, u.email as user_email, u.student_id
      FROM reservations r
      JOIN books b ON r.book_id = b.id
      JOIN users u ON r.user_id = u.id
      WHERE r.id = ?
    `;
    
    db.get(query, [id], callback);
  }

  static getByUser(userId, callback) {
    const query = `
      SELECT r.*, 
             b.title as book_title, b.author as book_author, b.isbn, b.available_copies
      FROM reservations r
      JOIN books b ON r.book_id = b.id
      WHERE r.user_id = ? AND r.status = 'active'
      ORDER BY r.priority DESC, r.reserved_date ASC
    `;
    
    db.all(query, [userId], callback);
  }

  static create(reservation, callback) {
    const { book_id, user_id, priority, notes } = reservation;
    
    // Check if user already has a reservation for this book
    db.get(
      'SELECT id FROM reservations WHERE book_id = ? AND user_id = ? AND status = "active"',
      [book_id, user_id],
      (err, existing) => {
        if (err) return callback(err);
        if (existing) {
          return callback(new Error('User already has an active reservation for this book'));
        }
        
        // Set expiration date (30 days from now)
        const expiresAt = new Date();
        expiresAt.setDate(expiresAt.getDate() + 30);
        
        db.run(
          'INSERT INTO reservations (book_id, user_id, priority, notes, expires_at) VALUES (?, ?, ?, ?, ?)',
          [book_id, user_id, priority || 1, notes, expiresAt.toISOString()],
          function(err) {
            if (err) return callback(err);
            callback(null, { id: this.lastID, ...reservation, expires_at: expiresAt });
          }
        );
      }
    );
  }

  static update(id, reservation, callback) {
    const { status, priority, notes } = reservation;
    
    db.run(
      'UPDATE reservations SET status = ?, priority = ?, notes = ? WHERE id = ?',
      [status, priority, notes, id],
      callback
    );
  }

  static cancel(id, callback) {
    db.run(
      'UPDATE reservations SET status = "cancelled" WHERE id = ?',
      [id],
      callback
    );
  }

  static fulfill(id, callback) {
    db.run(
      'UPDATE reservations SET status = "fulfilled" WHERE id = ?',
      [id],
      callback
    );
  }

  static delete(id, callback) {
    db.run('DELETE FROM reservations WHERE id = ?', [id], callback);
  }

  static getStats(callback) {
    const query = `
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN status = 'active' THEN 1 END) as active,
        COUNT(CASE WHEN status = 'fulfilled' THEN 1 END) as fulfilled,
        COUNT(CASE WHEN status = 'cancelled' THEN 1 END) as cancelled,
        COUNT(CASE WHEN status = 'expired' THEN 1 END) as expired
      FROM reservations
    `;
    
    db.get(query, callback);
  }

  // Clean up expired reservations
  static cleanupExpired(callback) {
    const now = new Date().toISOString();
    db.run(
      'UPDATE reservations SET status = "expired" WHERE expires_at < ? AND status = "active"',
      [now],
      callback
    );
  }
}

module.exports = Reservation;
