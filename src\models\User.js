const sqlite3 = require('sqlite3').verbose();
const bcrypt = require('bcryptjs');
const db = new sqlite3.Database('./library.db');

// Create users table if it doesn't exist
db.run(`
  CREATE TABLE IF NOT EXISTS users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    full_name TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    phone TEXT,
    password TEXT NOT NULL,
    user_type TEXT NOT NULL CHECK(user_type IN ('library_admin', 'student')),
    student_id TEXT UNIQUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    is_active INTEGER DEFAULT 1,
    is_blocked INTEGER DEFAULT 0
  )
`);

// Add is_blocked column if it doesn't exist (for existing databases)
db.run(`ALTER TABLE users ADD COLUMN is_blocked INTEGER DEFAULT 0`, (err) => {
  if (err && !err.message.includes('duplicate column name')) {
    console.error('Error adding is_blocked column:', err.message);
  }
});

// Create default admin user if it doesn't exist
db.get('SELECT * FROM users WHERE email = ?', ['<EMAIL>'], (err, user) => {
  if (!user) {
    const hashedPassword = bcrypt.hashSync('admin123', 10);
    db.run(
      'INSERT INTO users (full_name, email, password, user_type) VALUES (?, ?, ?, ?)',
      ['Library Administrator', '<EMAIL>', hashedPassword, 'library_admin']
    );
  }
});

// Create default demo student if it doesn't exist
db.get('SELECT * FROM users WHERE email = ?', ['<EMAIL>'], (err, user) => {
  if (!user) {
    const hashedPassword = bcrypt.hashSync('student123', 10);
    db.run(
      'INSERT INTO users (full_name, email, phone, password, user_type, student_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)',
      ['Demo Student', '<EMAIL>', '555-0123', hashedPassword, 'student', 'DEMO001', 1]
    );
  }
});



class User {
  static getAll(callback) {
    db.all('SELECT id, full_name, email, phone, user_type, student_id, created_at, is_active FROM users', callback);
  }

  static getById(id, callback) {
    db.get('SELECT id, full_name, email, phone, user_type, student_id, created_at, is_active FROM users WHERE id = ?', [id], callback);
  }

  static getByEmail(email, callback) {
    db.get('SELECT * FROM users WHERE email = ?', [email], callback);
  }

  static create(user, callback) {
    const { full_name, email, phone, password, user_type, student_id } = user;
    const hashedPassword = bcrypt.hashSync(password, 10);
    
    db.run(
      'INSERT INTO users (full_name, email, phone, password, user_type, student_id) VALUES (?, ?, ?, ?, ?, ?)',
      [full_name, email, phone, hashedPassword, user_type, student_id],
      function(err) {
        if (err) {
          callback(err);
        } else {
          callback(null, { id: this.lastID, full_name, email, phone, user_type, student_id });
        }
      }
    );
  }

  static update(id, user, callback) {
    // First get the current user data to preserve existing values
    this.getById(id, (err, currentUser) => {
      if (err) return callback(err);
      if (!currentUser) return callback(new Error('User not found'));

      // Merge current data with updates, preserving required fields
      const updatedUser = {
        full_name: user.full_name && user.full_name.trim() ? user.full_name.trim() : currentUser.full_name,
        email: user.email && user.email.trim() ? user.email.trim() : currentUser.email,
        phone: user.phone !== undefined ? (user.phone ? user.phone.trim() : user.phone) : currentUser.phone,
        user_type: user.user_type && user.user_type.trim() ? user.user_type.trim() : currentUser.user_type,
        student_id: user.student_id !== undefined ? (user.student_id ? user.student_id.trim() : user.student_id) : currentUser.student_id,
        is_active: user.is_active !== undefined ? user.is_active : currentUser.is_active,
        is_blocked: user.is_blocked !== undefined ? user.is_blocked : (currentUser.is_blocked || 0)
      };

      // Validate required fields
      if (!updatedUser.full_name || !updatedUser.email || !updatedUser.user_type) {
        return callback(new Error('Full name, email, and user type are required'));
      }

      db.run(
        'UPDATE users SET full_name = ?, email = ?, phone = ?, user_type = ?, student_id = ?, is_active = ?, is_blocked = ? WHERE id = ?',
        [updatedUser.full_name, updatedUser.email, updatedUser.phone, updatedUser.user_type, updatedUser.student_id, updatedUser.is_active, updatedUser.is_blocked, id],
        callback
      );
    });
  }

  static updatePassword(id, newPassword, callback) {
    const hashedPassword = bcrypt.hashSync(newPassword, 10);
    db.run('UPDATE users SET password = ? WHERE id = ?', [hashedPassword, id], callback);
  }

  static delete(id, callback) {
    db.run('DELETE FROM users WHERE id = ?', [id], callback);
  }

  static authenticate(email, password, callback) {
    this.getByEmail(email, (err, user) => {
      if (err) return callback(err);
      if (!user) return callback(null, false);
      if (!user.is_active) return callback(null, false);
      
      const isValid = bcrypt.compareSync(password, user.password);
      if (isValid) {
        // Remove password from returned user object
        const { password: _, ...userWithoutPassword } = user;
        callback(null, userWithoutPassword);
      } else {
        callback(null, false);
      }
    });
  }

  static getStudents(callback) {
    db.all('SELECT id, full_name, email, student_id FROM users WHERE user_type = "student" AND is_active = 1 ORDER BY full_name', (err, students) => {
      console.log('Students found:', students ? students.length : 0);
      callback(err, students);
    });
  }

  static softDelete(id, callback) {
    db.run('UPDATE users SET is_active = 0 WHERE id = ?', [id], callback);
  }

  static restore(id, callback) {
    db.run('UPDATE users SET is_active = 1 WHERE id = ?', [id], callback);
  }

  static blockUser(id, callback) {
    db.run('UPDATE users SET is_blocked = 1 WHERE id = ?', [id], callback);
  }

  static unblockUser(id, callback) {
    db.run('UPDATE users SET is_blocked = 0 WHERE id = ?', [id], callback);
  }

  static getAllIncludingDeleted(callback) {
    db.all('SELECT id, full_name, email, phone, user_type, student_id, is_active, is_blocked, created_at FROM users ORDER BY created_at DESC', callback);
  }
}

// Function to create sample students (called after server starts)
User.createSampleStudents = function() {
  db.get('SELECT COUNT(*) as count FROM users WHERE user_type = "student"', (err, result) => {
    if (!err && result.count <= 1) { // Only demo student exists
      console.log('Adding sample students...');
      const sampleStudents = [
        {
          full_name: 'Alice Johnson',
          email: '<EMAIL>',
          phone: '555-0124',
          student_id: 'STU001'
        },
        {
          full_name: 'Bob Smith',
          email: '<EMAIL>',
          phone: '555-0125',
          student_id: 'STU002'
        },
        {
          full_name: 'Carol Davis',
          email: '<EMAIL>',
          phone: '555-0126',
          student_id: 'STU003'
        },
        {
          full_name: 'David Wilson',
          email: '<EMAIL>',
          phone: '555-0127',
          student_id: 'STU004'
        },
        {
          full_name: 'Emma Brown',
          email: '<EMAIL>',
          phone: '555-0128',
          student_id: 'STU005'
        }
      ];

      sampleStudents.forEach((student, index) => {
        const hashedPassword = bcrypt.hashSync('student123', 10);
        db.run(
          'INSERT INTO users (full_name, email, phone, password, user_type, student_id, is_active) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [student.full_name, student.email, student.phone, hashedPassword, 'student', student.student_id, 1],
          (err) => {
            if (err) {
              console.error('Error inserting sample student:', err.message);
            } else if (index === sampleStudents.length - 1) {
              console.log('Sample students added successfully.');
            }
          }
        );
      });
    }
  });
};

module.exports = User;
